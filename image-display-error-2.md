This time i could not upload image and got network error… i got it when i finally clicked the add transaction button.
Console log:

Auth state changed: INITIAL_SESSION null
2D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\languageService.js:76 Language changed to: en
D:\currentprojects\LATEST\KharchaPani\khrpani\src\context\AuthContext.js:32 Auth state changed: SIGNED_IN Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:18 === ROLE CHECK ===
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:19 Current user role: null
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:20 User email: <EMAIL>
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:21 Is superadmin: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:22 Can manage roles: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:23 ==================
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:18 === ROLE CHECK ===
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:19 Current user role: superadmin
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:20 User email: <EMAIL>
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:21 Is superadmin: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:22 Can manage roles: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:23 ==================
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\languageService.js:76 Language changed to: en
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\languageService.js:219 Applied language on login: en for issuer
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\auth\SmartLoginScreen.js:104 Login successful
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:46 🔄 Screen focused - refreshing transactions
2D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:47 🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:47 🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
2D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:57 ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzOTQxLCJleHAiOjE3NTI5MTc1NDF9.WEeJLD3UJNVC8i_aJazVSnQNx7ZSwr2SGd4brkSoD3w
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:57 ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzOTQxLCJleHAiOjE3NTI5MTc1NDF9.PeTV2W3-PKTMjmEairNn_7lXyDBohMhCuet0flntIn0
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:96 ❌ Image load error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:97 ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:96 ❌ Image load error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:97 ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\AddTransactionScreen.js:91 🎯 Starting image selection from camera...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:214 🎯 Starting image selection from camera...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:67 [expo-image-picker] `ImagePicker.MediaTypeOptions` have been deprecated. Use `ImagePicker.MediaType` or an array of `ImagePicker.MediaType` instead.
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@react-native\js-polyfills\console.js:654
overrideMethod @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-devtools-core\dist\backend.js:17416
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\Core\setUpDeveloperTools.js:40
registerWarning @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\LogBox\LogBox.js:171
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\LogBox\LogBox.js:84
parseMediaTypes @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\expo-image-picker\build\utils.js:12
mapDeprecatedOptions @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\expo-image-picker\build\utils.js:26
?anon_0_ @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\expo-image-picker\build\ImagePicker.js:123
asyncGeneratorStep @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3
_next @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:22
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:14
_launchCameraAsync @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\expo-image-picker\build\ImagePicker.js:125
launchCameraAsync @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\expo-image-picker\build\ImagePicker.js:119
?anon_0_ @ D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:67
asyncGeneratorStep @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3
_next @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:17
Show 16 more frames
Show less
D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@react-native\js-polyfills\console.js:654 Cannot connect to Metro.

Try the following to fix the issue:
- Ensure that Metro is running and available on the same network
- Ensure that your device/emulator is connected to your machine and has USB debugging enabled - run 'adb devices' to see a list of connected devices
- If you're on a physical device connected to the same machine, run 'adb reverse tcp:8081 tcp:8081' to forward requests from your device
- If your device is on the same Wi-Fi network, set 'Debug server host & port for device' in 'Dev settings' to your machine's IP address and the port of the local dev server - e.g. ********:8081

URL: ***********:8081

Error: Software caused connection abort
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@react-native\js-polyfills\console.js:654
overrideMethod @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-devtools-core\dist\backend.js:17416
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\Core\setUpDeveloperTools.js:40
registerWarning @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\LogBox\LogBox.js:171
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\LogBox\LogBox.js:84
setHMRUnavailableReason @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\Utilities\HMRClient.js:292
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\Utilities\HMRClient.js:200
emit @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\metro-runtime\src\modules\vendor\eventemitter3.js:79
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\metro-runtime\src\modules\HMRClient.js:29
dispatchEvent @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\event-target-shim\dist\event-target-shim.mjs:814
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\WebSocket\WebSocket_old.js:278
emit @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\vendor\emitter\EventEmitter.js:126
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\EventEmitter\RCTDeviceEventEmitter.js:14
emit @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\EventEmitter\RCTDeviceEventEmitter.js:33
Show 14 more frames
Show less
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:123 🔄 Starting image compression...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:124 📷 Original image URI: file:///data/user/0/host.exp.exponent/cache/ExperienceData/%2540anonymous%252Fkharchapani-912676bb-4b53-440b-9473-b9fb98b5164f/ImagePicker/36cd5936-4474-4ceb-ae55-b8aeb1e25d29.jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:128 📊 Original estimated size: 2.44 MB
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:152 ✅ Compression complete!
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:153 📊 Compressed size: 90.89 KB
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:154 📈 Compression ratio: 96.4%
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:235 🎉 Image selection and compression complete!
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:248 🔍 DEBUG: About to return result: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:249 🔍 DEBUG: Result success property: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\AddTransactionScreen.js:96 🔍 DEBUG: Raw result from service: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\AddTransactionScreen.js:97 🔍 DEBUG: Result type: object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\AddTransactionScreen.js:98 🔍 DEBUG: Result is null? false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\AddTransactionScreen.js:99 🔍 DEBUG: Result is undefined? false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\AddTransactionScreen.js:102 ✅ Image selection successful: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:134 📸 Uploading transaction image...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:139 📊 Upload progress: 10% - Checking usage limits...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:139 📊 Upload progress: 30% - Compressing image...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:123 🔄 Starting image compression...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:124 📷 Original image URI: file:///data/user/0/host.exp.exponent/cache/ImageManipulator/30df0a43-035a-405a-924b-4f75f26dd81e.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:128 📊 Original estimated size: 90.89 KB
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:152 ✅ Compression complete!
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:153 📊 Compressed size: 90.88 KB
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\imageCompressionService.js:154 📈 Compression ratio: 0.0%
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:139 📊 Upload progress: 50% - Validating image...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:139 📊 Upload progress: 70% - Preparing upload...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:139 📊 Upload progress: 80% - Uploading to server...
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\storageService.js:165 📤 Uploading blob: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:139 📊 Upload progress: 0% - Error: Network request failed
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:186 ❌ Error creating transaction: TypeError: Network request failed
    at anonymous (***********:8081/node_modules/expo/AppEntry.bundle//&platform=android&dev=true&hot=false&transform.engine=hermes&transform.bytecode=1&transform.routerRoot=app&unstable_transformProfile=hermes-stable:15843:33)
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@react-native\js-polyfills\console.js:654
overrideMethod @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-devtools-core\dist\backend.js:17416
reactConsoleErrorHandler @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\Core\ExceptionsManager.js:182
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\Core\setUpDeveloperTools.js:40
registerError @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\LogBox\LogBox.js:231
anonymous @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\react-native\Libraries\LogBox\LogBox.js:80
?anon_0_ @ D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\transactionService.js:186
asyncGeneratorStep @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:3
_throw @ D:\currentprojects\LATEST\KharchaPani\khrpani\node_modules\@babel\runtime\helpers\asyncToGenerator.js:20
Show 8 more frames
Show less
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:46 🔄 Screen focused - refreshing transactions
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:46 🔄 Screen focused - refreshing transactions
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82 ❌ ImagePreview Error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86 ❌ ImagePreview: No image source or error, showing placeholder
Welcome to React Native DevTools
Debugger integration: Android Bridgeless (ReactHostImpl)

CMD Window logs:

Android Bundled 7765ms node_modules\expo\AppEntry.js (1136 modules)
 WARN  Due to changes in Androids permission requirements, Expo Go can no longer provide full access to the media library. To test the full functionality of this module, you can create a development build. https://docs.expo.dev/develop/development-builds/create-a-build
 LOG  Auth state changed: INITIAL_SESSION null
 LOG  Language changed to: en
 LOG  Language changed to: en
 INFO  Launching DevTools...
 LOG  Auth state changed: SIGNED_IN {"access_token": "eyJhbGciOiJIUzI1NiIsImtpZCI6IkNHd21Ta1p6U01MSkF4VWUiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.z2z4ID_nmZy73l80Y_b4r_BlF-PvUiyzzpS5p6tKqp8", "expires_at": **********, "expires_in": 3600, "refresh_token": "zjvdgtsspfky", "token_type": "bearer", "user": {"app_metadata": {"provider": "email", "providers": [Array]}, "aud": "authenticated", "confirmation_sent_at": "2025-07-17T14:33:57.857222Z", "confirmed_at": "2025-07-17T14:35:14.499821Z", "created_at": "2025-07-17T14:33:57.834335Z", "email": "<EMAIL>", "email_confirmed_at": "2025-07-17T14:35:14.499821Z", "id": "eb760dc2-c08a-4b37-a541-5103e0ef5732", "identities": [[Object]], "is_anonymous": false, "last_sign_in_at": "2025-07-19T08:32:18.583855642Z", "phone": "", "role": "authenticated", "updated_at": "2025-07-19T08:32:18.586501Z", "user_metadata": {"email": "<EMAIL>", "email_verified": true, "full_name": "Prasadh Baapaat", "phone_verified": false, "sub": "eb760dc2-c08a-4b37-a541-5103e0ef5732"}}}
 LOG  === ROLE CHECK ===
 LOG  Current user role: null
 LOG  User email: <EMAIL>
 LOG  Is superadmin: false
 LOG  Can manage roles: false
 LOG  ==================
 LOG  === ROLE CHECK ===
 LOG  Current user role: superadmin
 LOG  User email: <EMAIL>
 LOG  Is superadmin: true
 LOG  Can manage roles: true
 LOG  ==================
 LOG  Language changed to: en
 LOG  Applied language on login: en for issuer
 LOG  Login successful
 LOG  🔄 Screen focused - refreshing transactions
 LOG  🖼️ Transaction has image_url
 LOG  🖼️ Transaction has image_url
 LOG  🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🖼️ Transaction has image_url
 LOG  🖼️ Transaction has image_url
 LOG  ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzOTQxLCJleHAiOjE3NTI5MTc1NDF9.WEeJLD3UJNVC8i_aJazVSnQNx7ZSwr2SGd4brkSoD3w
 LOG  ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzOTQxLCJleHAiOjE3NTI5MTc1NDF9.PeTV2W3-PKTMjmEairNn_7lXyDBohMhCuet0flntIn0
 LOG  ❌ Image load error: {"_dispatchInstances": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3434609994292259, "actualStartTime": 57333496.843801, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.035999998450279236, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.04346200078725815, "type": [Object], "updateQueue": null}, "actualDuration": 0.3026150017976761, "actualStartTime": 57333496.881339, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.14515399932861328, "actualStartTime": 57333398.809108, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.008845999836921692, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.008845999836921692, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3434609994292259, "actualStartTime": 57333496.843801, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.035999998450279236, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.04346200078725815, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.007462002336978912, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007462002336978912, "type": "RCTImageView", "updateQueue": null}, "_dispatchListeners": [Function handleImageError], "_targetInst": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3434609994292259, "actualStartTime": 57333496.843801, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.035999998450279236, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.04346200078725815, "type": [Object], "updateQueue": null}, "actualDuration": 0.3026150017976761, "actualStartTime": 57333496.881339, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.14515399932861328, "actualStartTime": 57333398.809108, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.008845999836921692, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.008845999836921692, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3434609994292259, "actualStartTime": 57333496.843801, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.035999998450279236, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.04346200078725815, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.007462002336978912, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007462002336978912, "type": "RCTImageView", "updateQueue": null}, "bubbles": undefined, "cancelable": undefined, "currentTarget": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3026150017976761, "actualStartTime": 57333496.881339, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.007462002336978912, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007462002336978912, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 600, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "defaultPrevented": undefined, "dispatchConfig": {"registrationName": "onError"}, "eventPhase": undefined, "isDefaultPrevented": [Function functionThatReturnsFalse], "isPropagationStopped": [Function functionThatReturnsFalse], "isTrusted": undefined, "nativeEvent": {"error": "unknown image format", "target": 600}, "target": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3026150017976761, "actualStartTime": 57333496.881339, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.007462002336978912, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007462002336978912, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 600, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "timeStamp": 1752913941803, "type": undefined}
 LOG  ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  ❌ Image load error: {"_dispatchInstances": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.47546200454235077, "actualStartTime": 57333459.765416, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03538500517606735, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043154001235961914, "type": [Object], "updateQueue": null}, "actualDuration": 0.43476899713277817, "actualStartTime": 57333459.802262, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.14546100050210953, "actualStartTime": 57333413.93157, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.008307002484798431, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.008307002484798431, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.47546200454235077, "actualStartTime": 57333459.765416, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03538500517606735, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043154001235961914, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.007768996059894562, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007768996059894562, "type": "RCTImageView", "updateQueue": null}, "_dispatchListeners": [Function handleImageError], "_targetInst": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.47546200454235077, "actualStartTime": 57333459.765416, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03538500517606735, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043154001235961914, "type": [Object], "updateQueue": null}, "actualDuration": 0.43476899713277817, "actualStartTime": 57333459.802262, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.14546100050210953, "actualStartTime": 57333413.93157, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.008307002484798431, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.008307002484798431, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.47546200454235077, "actualStartTime": 57333459.765416, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03538500517606735, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043154001235961914, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.007768996059894562, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007768996059894562, "type": "RCTImageView", "updateQueue": null}, "bubbles": undefined, "cancelable": undefined, "currentTarget": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.43476899713277817, "actualStartTime": 57333459.802262, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.007768996059894562, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007768996059894562, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 666, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "defaultPrevented": undefined, "dispatchConfig": {"registrationName": "onError"}, "eventPhase": undefined, "isDefaultPrevented": [Function functionThatReturnsFalse], "isPropagationStopped": [Function functionThatReturnsFalse], "isTrusted": undefined, "nativeEvent": {"error": "unknown image format", "target": 666}, "target": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.43476899713277817, "actualStartTime": 57333459.802262, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.007768996059894562, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007768996059894562, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 666, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "timeStamp": 1752913941832, "type": undefined}
 LOG  ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  ❌ ImagePreview Error: {"imageError": true, "imageUri": undefined, "imageUrl": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg", "signedUrl": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzOTQxLCJleHAiOjE3NTI5MTc1NDF9.PeTV2W3-PKTMjmEairNn_7lXyDBohMhCuet0flntIn0"}
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  ❌ ImagePreview Error: {"imageError": true, "imageUri": undefined, "imageUrl": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg", "signedUrl": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzOTQxLCJleHAiOjE3NTI5MTc1NDF9.WEeJLD3UJNVC8i_aJazVSnQNx7ZSwr2SGd4brkSoD3w"}
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  🎯 Starting image selection from camera...
 LOG  🎯 Starting image selection from camera...
 WARN  [expo-image-picker] `ImagePicker.MediaTypeOptions` have been deprecated. Use `ImagePicker.MediaType` or an array of `ImagePicker.MediaType` instead.