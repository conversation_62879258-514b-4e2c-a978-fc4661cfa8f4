import * as ImageManipulator from 'expo-image-manipulator';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';

/**
 * Image Compression Service
 * Handles image selection, compression, and upload preparation
 * 
 * Compression Settings:
 * - Max dimensions: 1024x1024px
 * - Quality: 80% (good balance of quality vs size)
 * - Format: JPEG (best compression for photos)
 * - Expected size reduction: 80-90%
 */

class ImageCompressionService {
  
  // Compression configuration
  static COMPRESSION_CONFIG = {
    maxWidth: 1024,
    maxHeight: 1024,
    quality: 0.8, // 80% quality
    format: ImageManipulator.SaveFormat.JPEG,
  };

  /**
   * Request camera permissions
   * @returns {Promise<boolean>} Permission granted status
   */
  static async requestCameraPermissions() {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      return false;
    }
  }

  /**
   * Request media library permissions
   * @returns {Promise<boolean>} Permission granted status
   */
  static async requestMediaLibraryPermissions() {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return false;
    }
  }

  /**
   * Take photo with camera
   * @returns {Promise<Object|null>} Image result or null if cancelled
   */
  static async takePhoto() {
    try {
      // Check permissions
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) {
        throw new Error('Camera permission denied');
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        // Remove aspect ratio to allow flexible cropping
        quality: 1, // Full quality - we'll compress later
      });

      if (result.canceled) {
        return null;
      }

      return result.assets[0];
    } catch (error) {
      console.error('Error taking photo:', error);
      throw error;
    }
  }

  /**
   * Pick image from gallery
   * @returns {Promise<Object|null>} Image result or null if cancelled
   */
  static async pickFromGallery() {
    try {
      // Check permissions
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) {
        throw new Error('Media library permission denied');
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        // Remove aspect ratio to allow flexible cropping
        quality: 1, // Full quality - we'll compress later
      });

      if (result.canceled) {
        return null;
      }

      return result.assets[0];
    } catch (error) {
      console.error('Error picking from gallery:', error);
      throw error;
    }
  }

  /**
   * Compress image using Expo ImageManipulator
   * @param {string} imageUri - URI of the image to compress
   * @returns {Promise<Object>} Compressed image result with size info
   */
  static async compressImage(imageUri) {
    try {
      console.log('🔄 Starting image compression...');
      console.log('📷 Original image URI:', imageUri);

      // Get original file size (estimate)
      const originalSize = await this.getImageSize(imageUri);
      console.log('📊 Original estimated size:', this.formatFileSize(originalSize));

      // Compress the image
      const compressedResult = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          // Resize to max dimensions while maintaining aspect ratio
          {
            resize: {
              width: this.COMPRESSION_CONFIG.maxWidth,
              height: this.COMPRESSION_CONFIG.maxHeight,
            },
          },
        ],
        {
          compress: this.COMPRESSION_CONFIG.quality,
          format: this.COMPRESSION_CONFIG.format,
        }
      );

      // Get compressed file size
      const compressedSize = await this.getImageSize(compressedResult.uri);
      const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

      console.log('✅ Compression complete!');
      console.log('📊 Compressed size:', this.formatFileSize(compressedSize));
      console.log('📈 Compression ratio:', `${compressionRatio}%`);

      return {
        ...compressedResult,
        originalSize,
        compressedSize,
        compressionRatio: parseFloat(compressionRatio),
        sizeReduction: originalSize - compressedSize,
      };
    } catch (error) {
      console.error('❌ Error compressing image:', error);
      throw error;
    }
  }

  /**
   * Get estimated image file size
   * @param {string} imageUri - URI of the image
   * @returns {Promise<number>} Estimated file size in bytes
   */
  static async getImageSize(imageUri) {
    try {
      // For local files, we can get actual size
      if (imageUri.startsWith('file://')) {
        const response = await fetch(imageUri);
        const blob = await response.blob();
        return blob.size;
      }
      
      // For other URIs, estimate based on dimensions
      // This is a rough estimate - actual size may vary
      return 2000000; // 2MB estimate for uncompressed images
    } catch (error) {
      console.warn('Could not get exact file size, using estimate:', error);
      return 2000000; // 2MB fallback estimate
    }
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted file size
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Complete image selection and compression workflow
   * @param {string} source - 'camera' or 'gallery'
   * @returns {Promise<Object|null>} Compressed image result or null if cancelled
   */
  static async selectAndCompressImage(source = 'camera') {
    try {
      console.log(`🎯 Starting image selection from ${source}...`);

      // Select image based on source
      let imageResult;
      if (source === 'camera') {
        imageResult = await this.takePhoto();
      } else if (source === 'gallery') {
        imageResult = await this.pickFromGallery();
      } else {
        throw new Error('Invalid source. Use "camera" or "gallery"');
      }

      // Return null if user cancelled
      if (!imageResult) {
        console.log('📱 User cancelled image selection');
        return null;
      }

      // Compress the selected image
      const compressedResult = await this.compressImage(imageResult.uri);

      console.log('🎉 Image selection and compression complete!');

      const finalResult = {
        success: true,
        compressedImage: compressedResult,
        originalSize: compressedResult.originalSize,
        compressedSize: compressedResult.compressedSize,
        compressionRatio: compressedResult.compressionRatio,
        originalImageInfo: imageResult,
        source,
        timestamp: new Date().toISOString(),
      };

      console.log('🔍 DEBUG: About to return result:', finalResult);
      console.log('🔍 DEBUG: Result success property:', finalResult.success);

      return finalResult;
    } catch (error) {
      console.error('❌ Error in image selection workflow:', error);
      return {
        success: false,
        error: error.message || 'Failed to select and compress image',
        source,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Validate compressed image meets our requirements
   * @param {Object} compressedImage - Compressed image result
   * @returns {Object} Validation result
   */
  static validateCompressedImage(compressedImage) {
    const maxSizeBytes = 1024 * 1024; // 1MB max
    const isValidSize = compressedImage.compressedSize <= maxSizeBytes;
    const isValidFormat = compressedImage.uri.toLowerCase().includes('.jpg') || 
                         compressedImage.uri.toLowerCase().includes('.jpeg');

    return {
      isValid: isValidSize && isValidFormat,
      size: compressedImage.compressedSize,
      maxSize: maxSizeBytes,
      format: isValidFormat ? 'JPEG' : 'Unknown',
      errors: [
        ...(!isValidSize ? [`Image too large: ${this.formatFileSize(compressedImage.compressedSize)} (max: ${this.formatFileSize(maxSizeBytes)})`] : []),
        ...(!isValidFormat ? ['Invalid format: Only JPEG images are supported'] : []),
      ],
    };
  }
}

export default ImageCompressionService;
