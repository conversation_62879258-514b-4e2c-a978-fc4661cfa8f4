import { supabase } from '../lib/supabase';

/**
 * Custodian Management Service
 * Handles CRUD operations for custodians
 */

/**
 * Create a new custodian
 * @param {string} custodianName - Full name of custodian
 * @param {string} phone - 10-digit phone number
 * @param {string} pin - 6-digit PIN
 * @param {string} language - Preferred language code
 * @returns {Promise<{success: boolean, data: object|null, error: string|null}>}
 */
export const createCustodian = async (custodianName, phone, pin, language = 'en') => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, data: null, error: 'User not authenticated' };
    }

    // Clean phone number (remove any spaces, dashes, etc.)
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    // Validate inputs
    if (!custodianName || custodianName.trim().length < 2) {
      return { success: false, data: null, error: 'Name must be at least 2 characters' };
    }
    
    if (!/^[0-9]{10}$/.test(cleanPhone)) {
      return { success: false, data: null, error: 'Phone number must be exactly 10 digits' };
    }
    
    if (!/^[0-9]{6}$/.test(pin)) {
      return { success: false, data: null, error: 'PIN must be exactly 6 digits' };
    }

    // Call database function to create custodian
    const { data, error } = await supabase.rpc('create_custodian', {
      target_issuer_id: user.id,
      custodian_name: custodianName.trim(),
      phone: cleanPhone,
      pin: pin,
      language: language
    });

    if (error) {
      console.error('Error creating custodian:', error);
      return { success: false, data: null, error: error.message };
    }

    const result = data?.[0];
    
    if (!result?.success) {
      return { success: false, data: null, error: result?.message || 'Failed to create custodian' };
    }

    return {
      success: true,
      data: {
        custodianId: result.custodian_id,
        message: result.message
      },
      error: null
    };

  } catch (error) {
    console.error('Error in createCustodian:', error);
    return { success: false, data: null, error: 'Failed to create custodian. Please try again.' };
  }
};

/**
 * Get all custodians for the current issuer
 * @returns {Promise<{success: boolean, data: Array|null, error: string|null}>}
 */
export const getCustodians = async () => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, data: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase.rpc('get_issuer_custodians', {
      issuer_id: user.id
    });

    if (error) {
      console.error('Error fetching custodians:', error);
      return { success: false, data: null, error: error.message };
    }

    return { success: true, data: data || [], error: null };

  } catch (error) {
    console.error('Error in getCustodians:', error);
    return { success: false, data: null, error: 'Failed to fetch custodians' };
  }
};

/**
 * Get user's plan information and custodian usage
 * @returns {Promise<{success: boolean, data: object|null, error: string|null}>}
 */
export const getPlanInfo = async () => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, data: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase.rpc('get_user_plan_info', {
      target_user_id: user.id
    });

    if (error) {
      console.error('Error fetching plan info:', error);
      return { success: false, data: null, error: error.message };
    }

    return { success: true, data: data?.[0] || null, error: null };

  } catch (error) {
    console.error('Error in getPlanInfo:', error);
    return { success: false, data: null, error: 'Failed to fetch plan information' };
  }
};

/**
 * Toggle custodian active status
 * @param {string} custodianId - Custodian ID
 * @param {boolean} isActive - New active status
 * @returns {Promise<{success: boolean, error: string|null}>}
 */
export const toggleCustodianStatus = async (custodianId, isActive) => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { error } = await supabase
      .from('custodians')
      .update({ 
        is_active: isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', custodianId)
      .eq('issuer_user_id', user.id); // Ensure user can only modify their own custodians

    if (error) {
      console.error('Error updating custodian status:', error);
      return { success: false, error: error.message };
    }

    return { success: true, error: null };

  } catch (error) {
    console.error('Error in toggleCustodianStatus:', error);
    return { success: false, error: 'Failed to update custodian status' };
  }
};

/**
 * Update custodian PIN
 * @param {string} custodianId - Custodian ID
 * @param {string} newPin - New 6-digit PIN
 * @returns {Promise<{success: boolean, error: string|null}>}
 */
export const updateCustodianPin = async (custodianId, newPin) => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    // Validate PIN
    if (!/^[0-9]{6}$/.test(newPin)) {
      return { success: false, error: 'PIN must be exactly 6 digits' };
    }

    // Use SQL function to hash the PIN properly
    const { error } = await supabase.rpc('update_custodian_pin', {
      custodian_id: custodianId,
      issuer_id: user.id,
      new_pin: newPin
    });

    if (error) {
      console.error('Error updating custodian PIN:', error);
      return { success: false, error: error.message };
    }

    return { success: true, error: null };

  } catch (error) {
    console.error('Error in updateCustodianPin:', error);
    return { success: false, error: 'Failed to update PIN' };
  }
};

/**
 * Delete custodian (soft delete - set inactive)
 * @param {string} custodianId - Custodian ID
 * @returns {Promise<{success: boolean, error: string|null}>}
 */
export const deleteCustodian = async (custodianId) => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { error } = await supabase
      .from('custodians')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', custodianId)
      .eq('issuer_user_id', user.id); // Ensure user can only delete their own custodians

    if (error) {
      console.error('Error deleting custodian:', error);
      return { success: false, error: error.message };
    }

    return { success: true, error: null };

  } catch (error) {
    console.error('Error in deleteCustodian:', error);
    return { success: false, error: 'Failed to delete custodian' };
  }
};

/**
 * Format phone number for display (add spaces for readability)
 * @param {string} phone - 10-digit phone number
 * @returns {string} - Formatted phone number
 */
export const formatPhoneNumber = (phone) => {
  if (!phone || phone.length !== 10) return phone;
  
  // Format as: 98765 43210
  return `${phone.slice(0, 5)} ${phone.slice(5)}`;
};

/**
 * Get plan display info
 * @param {string} planCode - Plan code
 * @returns {object} - Plan display information
 */
export const getPlanDisplayInfo = (planCode) => {
  const planInfo = {
    free: { name: 'Free Plan', color: '#666', emoji: '🆓' },
    starter: { name: 'Starter Plan', color: '#4CAF50', emoji: '🚀' },
    professional: { name: 'Professional Plan', color: '#2196F3', emoji: '⭐' },
    enterprise: { name: 'Enterprise Plan', color: '#9C27B0', emoji: '👑' }
  };
  
  return planInfo[planCode] || { name: 'Unknown Plan', color: '#666', emoji: '❓' };
};

export default {
  createCustodian,
  getCustodians,
  getPlanInfo,
  toggleCustodianStatus,
  updateCustodianPin,
  deleteCustodian,
  formatPhoneNumber,
  getPlanDisplayInfo
};
