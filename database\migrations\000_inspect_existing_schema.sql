-- =====================================================
-- Database Schema Inspector
-- =====================================================
-- Run this FIRST to see what already exists in your database
-- This will help us create a perfect migration without conflicts

-- 1. Check if subscription_plans table exists and show its structure
DO $$
DECLARE
    rec RECORD;
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subscription_plans') THEN
        RAISE NOTICE '=== SUBSCRIPTION_PLANS TABLE EXISTS ===';

        -- Show all columns
        FOR rec IN
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'subscription_plans'
            ORDER BY ordinal_position
        LOOP
            RAISE NOTICE 'Column: % | Type: % | Nullable: % | Default: %',
                rec.column_name, rec.data_type, rec.is_nullable, rec.column_default;
        END LOOP;

        -- Show constraints
        FOR rec IN
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'subscription_plans'
        LOOP
            RAISE NOTICE 'Constraint: % | Type: %', rec.constraint_name, rec.constraint_type;
        END LOOP;

        -- Show existing data
        FOR rec IN
            SELECT plan_name, custodian_limit
            FROM subscription_plans
        LOOP
            RAISE NOTICE 'Existing Plan: % | Custodian Limit: %', rec.plan_name, rec.custodian_limit;
        END LOOP;

    ELSE
        RAISE NOTICE '=== SUBSCRIPTION_PLANS TABLE DOES NOT EXIST ===';
    END IF;
END $$;

-- 2. Check user_roles table structure
DO $$
DECLARE
    rec RECORD;
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_roles') THEN
        RAISE NOTICE '=== USER_ROLES TABLE EXISTS ===';

        FOR rec IN
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'user_roles'
            ORDER BY ordinal_position
        LOOP
            RAISE NOTICE 'Column: % | Type: % | Nullable: % | Default: %',
                rec.column_name, rec.data_type, rec.is_nullable, rec.column_default;
        END LOOP;
    ELSE
        RAISE NOTICE '=== USER_ROLES TABLE DOES NOT EXIST ===';
    END IF;
END $$;

-- 3. Check custodians table structure
DO $$
DECLARE
    rec RECORD;
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'custodians') THEN
        RAISE NOTICE '=== CUSTODIANS TABLE EXISTS ===';

        FOR rec IN
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_name = 'custodians'
            ORDER BY ordinal_position
        LOOP
            RAISE NOTICE 'Column: % | Type: % | Nullable: % | Default: %',
                rec.column_name, rec.data_type, rec.is_nullable, rec.column_default;
        END LOOP;
    ELSE
        RAISE NOTICE '=== CUSTODIANS TABLE DOES NOT EXIST ===';
    END IF;
END $$;

-- 4. Check what other tables exist that might be relevant
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== ALL EXISTING TABLES ===';

    FOR rec IN
        SELECT table_name, table_type
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name NOT LIKE 'pg_%'
        ORDER BY table_name
    LOOP
        RAISE NOTICE 'Table: % | Type: %', rec.table_name, rec.table_type;
    END LOOP;
END $$;

-- 5. Check existing functions that might be relevant
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== EXISTING FUNCTIONS ===';

    FOR rec IN
        SELECT routine_name, routine_type
        FROM information_schema.routines
        WHERE routine_schema = 'public'
        AND routine_name IN ('update_updated_at_column', 'create_custodian', 'authenticate_custodian')
    LOOP
        RAISE NOTICE 'Function: % | Type: %', rec.routine_name, rec.routine_type;
    END LOOP;
END $$;

-- 6. Check existing RLS policies
DO $$
DECLARE
    rec RECORD;
BEGIN
    RAISE NOTICE '=== EXISTING RLS POLICIES ===';

    FOR rec IN
        SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
        FROM pg_policies
        WHERE schemaname = 'public'
    LOOP
        RAISE NOTICE 'Policy: % on table % | Command: % | Roles: %',
            rec.policyname, rec.tablename, rec.cmd, rec.roles;
    END LOOP;
END $$;

-- 7. Check storage buckets (if any)
DO $$
DECLARE
    rec RECORD;
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'buckets' AND table_schema = 'storage') THEN
        RAISE NOTICE '=== STORAGE BUCKETS ===';

        FOR rec IN
            SELECT name, public
            FROM storage.buckets
        LOOP
            RAISE NOTICE 'Bucket: % | Public: %', rec.name, rec.public;
        END LOOP;
    ELSE
        RAISE NOTICE '=== NO STORAGE BUCKETS FOUND ===';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '=== STORAGE SCHEMA NOT ACCESSIBLE ===';
END $$;
