import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useAuth } from '../context/AuthContext';
import RoleGuard, { FeatureGuard, AdminOnly, SuperAdminOnly, PremiumOnly } from '../components/RoleGuard';
import { colors } from '../constants/colors';

/**
 * Example Component showing how to use the Role System
 * 
 * This demonstrates all the different ways to control access
 * based on user roles and features.
 */

const RoleSystemExample = () => {
  const { userRole, hasFeatureAccess, hasRole, hasAnyRole } = useAuth();

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Role System Demo</Text>
      
      {/* Current User Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current User Info</Text>
        <Text style={styles.infoText}>Role: {userRole || 'Loading...'}</Text>
        <Text style={styles.infoText}>
          Is Admin: {hasAnyRole(['admin', 'superadmin']) ? 'Yes' : 'No'}
        </Text>
        <Text style={styles.infoText}>
          Is Premium: {hasFeatureAccess('voice_memos') ? 'Yes' : 'No'}
        </Text>
      </View>

      {/* Basic Role Guards */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Role-Based Content</Text>
        
        {/* Free User Content (everyone sees this) */}
        <View style={styles.featureBox}>
          <Text style={styles.featureTitle}>✅ Basic Features (All Users)</Text>
          <Text style={styles.featureDesc}>Record transactions, view balance</Text>
        </View>

        {/* Premium User Content */}
        <PremiumOnly fallback={
          <View style={styles.lockedFeatureBox}>
            <Text style={styles.lockedFeatureTitle}>🔒 Premium Features</Text>
            <Text style={styles.lockedFeatureDesc}>Upgrade to unlock voice memos, bill photos, and advanced reports</Text>
            <TouchableOpacity style={styles.upgradeButton}>
              <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
            </TouchableOpacity>
          </View>
        }>
          <View style={styles.featureBox}>
            <Text style={styles.featureTitle}>⭐ Premium Features</Text>
            <Text style={styles.featureDesc}>Voice memos, bill photos, advanced reports</Text>
          </View>
        </PremiumOnly>

        {/* Admin Content */}
        <AdminOnly fallback={
          <View style={styles.lockedFeatureBox}>
            <Text style={styles.lockedFeatureTitle}>🔒 Admin Panel</Text>
            <Text style={styles.lockedFeatureDesc}>Admin access required</Text>
          </View>
        }>
          <View style={styles.featureBox}>
            <Text style={styles.featureTitle}>👨‍💼 Admin Features</Text>
            <Text style={styles.featureDesc}>User analytics, customer support</Text>
          </View>
        </AdminOnly>

        {/* Superadmin Content */}
        <SuperAdminOnly fallback={
          <View style={styles.lockedFeatureBox}>
            <Text style={styles.lockedFeatureTitle}>🔒 Superadmin Panel</Text>
            <Text style={styles.lockedFeatureDesc}>Superadmin access required</Text>
          </View>
        }>
          <View style={styles.featureBox}>
            <Text style={styles.featureTitle}>👑 Superadmin Features</Text>
            <Text style={styles.featureDesc}>Role management, system settings</Text>
          </View>
        </SuperAdminOnly>
      </View>

      {/* Feature-Based Guards */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Feature-Based Access</Text>
        
        <FeatureGuard 
          feature="voice_memos"
          fallback={
            <View style={styles.lockedFeatureBox}>
              <Text style={styles.lockedFeatureTitle}>🎤 Voice Memos</Text>
              <Text style={styles.lockedFeatureDesc}>Premium feature - upgrade to unlock</Text>
            </View>
          }
        >
          <TouchableOpacity style={styles.featureButton}>
            <Text style={styles.featureButtonText}>🎤 Record Voice Memo</Text>
          </TouchableOpacity>
        </FeatureGuard>

        <FeatureGuard 
          feature="bill_photos"
          fallback={
            <View style={styles.lockedFeatureBox}>
              <Text style={styles.lockedFeatureTitle}>📷 Bill Photos</Text>
              <Text style={styles.lockedFeatureDesc}>Premium feature - upgrade to unlock</Text>
            </View>
          }
        >
          <TouchableOpacity style={styles.featureButton}>
            <Text style={styles.featureButtonText}>📷 Attach Bill Photo</Text>
          </TouchableOpacity>
        </FeatureGuard>

        <FeatureGuard 
          feature="advanced_reports"
          fallback={
            <View style={styles.lockedFeatureBox}>
              <Text style={styles.lockedFeatureTitle}>📊 Advanced Reports</Text>
              <Text style={styles.lockedFeatureDesc}>Premium feature - upgrade to unlock</Text>
            </View>
          }
        >
          <TouchableOpacity style={styles.featureButton}>
            <Text style={styles.featureButtonText}>📊 Generate Advanced Report</Text>
          </TouchableOpacity>
        </FeatureGuard>
      </View>

      {/* Custom Role Logic */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom Role Logic</Text>
        
        {hasRole('superadmin') && (
          <View style={styles.featureBox}>
            <Text style={styles.featureTitle}>🛠️ Custom Superadmin Logic</Text>
            <Text style={styles.featureDesc}>This uses hasRole('superadmin') check</Text>
          </View>
        )}

        {hasAnyRole(['admin', 'superadmin']) && (
          <View style={styles.featureBox}>
            <Text style={styles.featureTitle}>📈 Analytics Dashboard</Text>
            <Text style={styles.featureDesc}>Available for admins and superadmins</Text>
          </View>
        )}

        {hasFeatureAccess('export_data') && (
          <TouchableOpacity style={styles.featureButton}>
            <Text style={styles.featureButtonText}>💾 Export Data</Text>
          </TouchableOpacity>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
    marginBottom: 24,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: colors.text, // Global text color
    marginBottom: 4,
  },
  featureBox: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  lockedFeatureBox: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: colors.border,
    opacity: 0.7,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text, // Global text color
    marginBottom: 4,
  },
  lockedFeatureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text, // Global text color
    marginBottom: 4,
  },
  featureDesc: {
    fontSize: 14,
    color: colors.text, // Global text color
  },
  lockedFeatureDesc: {
    fontSize: 14,
    color: colors.text, // Global text color
    fontStyle: 'italic',
  },
  featureButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    padding: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  featureButtonText: {
    color: colors.background,
    fontSize: 14,
    fontWeight: 'bold',
  },
  upgradeButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    padding: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  upgradeButtonText: {
    color: colors.background,
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default RoleSystemExample;
