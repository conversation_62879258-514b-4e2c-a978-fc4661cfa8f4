import AsyncStorage from '@react-native-async-storage/async-storage';
import i18n from '../i18n/i18n';
import { supabase } from '../lib/supabase';

/**
 * Language Service
 * Handles language preference storage, retrieval, and application
 */

export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  hi: {
    code: 'hi',
    name: 'Hindi',
    nativeName: 'हिंदी',
    flag: '🇮🇳'
  }
};

const LANGUAGE_STORAGE_KEY = 'user_language_preference';

/**
 * Get user's stored language preference
 * @returns {Promise<string>} Language code
 */
export const getStoredLanguage = async () => {
  try {
    const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
    return storedLanguage || 'en'; // Default to English
  } catch (error) {
    console.error('Error getting stored language:', error);
    return 'en';
  }
};

/**
 * Store language preference locally
 * @param {string} languageCode - Language code to store
 */
export const storeLanguage = async (languageCode) => {
  try {
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);
  } catch (error) {
    console.error('Error storing language:', error);
  }
};

/**
 * Apply language to the app
 * @param {string} languageCode - Language code to apply
 */
export const applyLanguage = async (languageCode) => {
  try {
    if (SUPPORTED_LANGUAGES[languageCode]) {
      await i18n.changeLanguage(languageCode);
      await storeLanguage(languageCode);
      console.log(`Language changed to: ${languageCode}`);
    } else {
      console.warn(`Unsupported language: ${languageCode}, defaulting to English`);
      await i18n.changeLanguage('en');
      await storeLanguage('en');
    }
  } catch (error) {
    console.error('Error applying language:', error);
  }
};

/**
 * Initialize language on app start
 * Checks for stored preference and applies it
 */
export const initializeLanguage = async () => {
  try {
    const storedLanguage = await getStoredLanguage();
    await applyLanguage(storedLanguage);
  } catch (error) {
    console.error('Error initializing language:', error);
    // Fallback to English
    await applyLanguage('en');
  }
};

/**
 * Get user's language preference from database (for issuers)
 * @returns {Promise<{success: boolean, language: string|null, error: string|null}>}
 */
export const getUserLanguageFromDB = async () => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, language: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('user_roles')
      .select('preferred_language')
      .eq('user_id', user.id)
      .limit(1);

    if (error) {
      console.error('Error fetching user language:', error);
      return { success: false, language: null, error: error.message };
    }

    return {
      success: true,
      language: data?.[0]?.preferred_language || 'en',
      error: null
    };

  } catch (error) {
    console.error('Error in getUserLanguageFromDB:', error);
    return { success: false, language: null, error: error.message };
  }
};

/**
 * Update user's language preference in database (for issuers)
 * @param {string} languageCode - New language preference
 * @returns {Promise<{success: boolean, error: string|null}>}
 */
export const updateUserLanguageInDB = async (languageCode) => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'User not authenticated' };
    }

    const { error } = await supabase.rpc('update_user_language', {
      user_id: user.id,
      new_language: languageCode
    });

    if (error) {
      console.error('Error updating user language:', error);
      return { success: false, error: error.message };
    }

    return { success: true, error: null };

  } catch (error) {
    console.error('Error in updateUserLanguageInDB:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Set user language preference (both locally and in database)
 * @param {string} languageCode - Language code to set
 * @param {boolean} updateDB - Whether to update database (for issuers)
 * @returns {Promise<{success: boolean, error: string|null}>}
 */
export const setUserLanguage = async (languageCode, updateDB = true) => {
  try {
    // Apply language immediately
    await applyLanguage(languageCode);

    // Update database if requested (for issuers)
    if (updateDB) {
      const dbResult = await updateUserLanguageInDB(languageCode);
      if (!dbResult.success) {
        console.warn('Failed to update language in database:', dbResult.error);
        // Don't fail the operation, local storage is still updated
      }
    }

    return { success: true, error: null };

  } catch (error) {
    console.error('Error in setUserLanguage:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Load and apply user's language preference on login
 * @param {object} userSession - User session data
 * @param {string} userType - 'issuer' or 'custodian'
 */
export const loadUserLanguageOnLogin = async (userSession, userType) => {
  try {
    let languageToApply = 'en'; // Default

    if (userType === 'issuer') {
      // For issuers, get language from database
      const dbResult = await getUserLanguageFromDB();
      if (dbResult.success && dbResult.language) {
        languageToApply = dbResult.language;
      }
    } else if (userType === 'custodian' && userSession?.preferredLanguage) {
      // For custodians, use language from session data
      languageToApply = userSession.preferredLanguage;
    }

    // Apply the language
    await applyLanguage(languageToApply);
    
    console.log(`Applied language on login: ${languageToApply} for ${userType}`);

  } catch (error) {
    console.error('Error loading user language on login:', error);
    // Fallback to stored language or English
    await initializeLanguage();
  }
};

/**
 * Get language display information
 * @param {string} languageCode - Language code
 * @returns {object} Language display info
 */
export const getLanguageInfo = (languageCode) => {
  return SUPPORTED_LANGUAGES[languageCode] || SUPPORTED_LANGUAGES.en;
};

/**
 * Get all supported languages for selection
 * @returns {Array} Array of language objects
 */
export const getSupportedLanguages = () => {
  return Object.values(SUPPORTED_LANGUAGES);
};

export default {
  SUPPORTED_LANGUAGES,
  getStoredLanguage,
  storeLanguage,
  applyLanguage,
  initializeLanguage,
  getUserLanguageFromDB,
  updateUserLanguageInDB,
  setUserLanguage,
  loadUserLanguageOnLogin,
  getLanguageInfo,
  getSupportedLanguages
};
