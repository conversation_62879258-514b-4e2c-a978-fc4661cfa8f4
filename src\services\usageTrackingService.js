import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../lib/supabase';

/**
 * Usage Tracking Service
 * Tracks daily usage limits for images and other media
 * Enforces plan-based restrictions
 */

class UsageTrackingService {
  
  // Storage keys
  static STORAGE_KEYS = {
    DAILY_IMAGE_COUNT: 'daily_image_count',
    DAILY_VOICE_COUNT: 'daily_voice_count',
    LAST_RESET_DATE: 'last_reset_date',
  };

  // Plan limits configuration
  static PLAN_LIMITS = {
    free: {
      images_per_day: 3,
      voice_memos_per_day: 10,
      custodians: 1,
    },
    starter: {
      images_per_day: 15,
      voice_memos_per_day: 50,
      custodians: 5,
    },
    professional: {
      images_per_day: 50,
      voice_memos_per_day: 200,
      custodians: 15,
    },
    enterprise: {
      images_per_day: 200,
      voice_memos_per_day: 1000,
      custodians: 999,
    },
  };

  /**
   * Get current user's subscription plan
   * @param {string} userId - User ID
   * @returns {Promise<string>} Plan name (free, starter, professional, enterprise)
   */
  static async getUserPlan(userId) {
    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select(`
          subscription_plans (
            plan_name
          )
        `)
        .eq('user_id', userId)
        .single();

      if (error) {
        console.warn('Error fetching user plan, defaulting to free:', error);
        return 'free';
      }

      return data?.subscription_plans?.plan_name || 'free';
    } catch (error) {
      console.warn('Error in getUserPlan, defaulting to free:', error);
      return 'free';
    }
  }

  /**
   * Get plan limits for a specific plan
   * @param {string} planName - Plan name
   * @returns {Object} Plan limits
   */
  static getPlanLimits(planName) {
    return this.PLAN_LIMITS[planName] || this.PLAN_LIMITS.free;
  }

  /**
   * Check if we need to reset daily counters
   * @returns {Promise<boolean>} True if reset was performed
   */
  static async checkAndResetDailyCounters() {
    try {
      const today = new Date().toDateString();
      const lastResetDate = await AsyncStorage.getItem(this.STORAGE_KEYS.LAST_RESET_DATE);

      if (lastResetDate !== today) {
        // Reset all daily counters
        await AsyncStorage.multiSet([
          [this.STORAGE_KEYS.DAILY_IMAGE_COUNT, '0'],
          [this.STORAGE_KEYS.DAILY_VOICE_COUNT, '0'],
          [this.STORAGE_KEYS.LAST_RESET_DATE, today],
        ]);

        console.log('📅 Daily usage counters reset for new day');
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error resetting daily counters:', error);
      return false;
    }
  }

  /**
   * Get current daily usage count
   * @param {string} type - 'images' or 'voice'
   * @returns {Promise<number>} Current count
   */
  static async getDailyUsageCount(type) {
    try {
      await this.checkAndResetDailyCounters();

      const storageKey = type === 'images' 
        ? this.STORAGE_KEYS.DAILY_IMAGE_COUNT 
        : this.STORAGE_KEYS.DAILY_VOICE_COUNT;

      const count = await AsyncStorage.getItem(storageKey);
      return parseInt(count || '0', 10);
    } catch (error) {
      console.error('Error getting daily usage count:', error);
      return 0;
    }
  }

  /**
   * Increment daily usage count
   * @param {string} type - 'images' or 'voice'
   * @param {number} increment - Amount to increment (default: 1)
   * @returns {Promise<number>} New count
   */
  static async incrementDailyUsage(type, increment = 1) {
    try {
      const currentCount = await this.getDailyUsageCount(type);
      const newCount = currentCount + increment;

      const storageKey = type === 'images' 
        ? this.STORAGE_KEYS.DAILY_IMAGE_COUNT 
        : this.STORAGE_KEYS.DAILY_VOICE_COUNT;

      await AsyncStorage.setItem(storageKey, newCount.toString());

      console.log(`📊 ${type} usage incremented: ${currentCount} → ${newCount}`);
      return newCount;
    } catch (error) {
      console.error('Error incrementing daily usage:', error);
      throw error;
    }
  }

  /**
   * Check if user can upload more images today
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Usage check result
   */
  static async checkImageUploadLimit(userId) {
    try {
      const [userPlan, currentCount] = await Promise.all([
        this.getUserPlan(userId),
        this.getDailyUsageCount('images'),
      ]);

      const planLimits = this.getPlanLimits(userPlan);
      const limit = planLimits.images_per_day;
      const canUpload = currentCount < limit;
      const remaining = Math.max(0, limit - currentCount);

      return {
        canUpload,
        currentCount,
        limit,
        remaining,
        planName: userPlan,
        resetTime: this.getNextResetTime(),
      };
    } catch (error) {
      console.error('Error checking image upload limit:', error);
      // Default to allowing upload on error
      return {
        canUpload: true,
        currentCount: 0,
        limit: 3,
        remaining: 3,
        planName: 'free',
        resetTime: this.getNextResetTime(),
      };
    }
  }

  /**
   * Check if user can record more voice memos today
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Usage check result
   */
  static async checkVoiceRecordingLimit(userId) {
    try {
      const [userPlan, currentCount] = await Promise.all([
        this.getUserPlan(userId),
        this.getDailyUsageCount('voice'),
      ]);

      const planLimits = this.getPlanLimits(userPlan);
      const limit = planLimits.voice_memos_per_day;
      const canRecord = currentCount < limit;
      const remaining = Math.max(0, limit - currentCount);

      return {
        canRecord,
        currentCount,
        limit,
        remaining,
        planName: userPlan,
        resetTime: this.getNextResetTime(),
      };
    } catch (error) {
      console.error('Error checking voice recording limit:', error);
      // Default to allowing recording on error
      return {
        canRecord: true,
        currentCount: 0,
        limit: 10,
        remaining: 10,
        planName: 'free',
        resetTime: this.getNextResetTime(),
      };
    }
  }

  /**
   * Get next reset time (midnight)
   * @returns {Date} Next reset time
   */
  static getNextResetTime() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }

  /**
   * Get formatted time until next reset
   * @returns {string} Formatted time string
   */
  static getTimeUntilReset() {
    const now = new Date();
    const resetTime = this.getNextResetTime();
    const diffMs = resetTime.getTime() - now.getTime();
    
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }

  /**
   * Get comprehensive usage summary for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Complete usage summary
   */
  static async getUsageSummary(userId) {
    try {
      const [imageUsage, voiceUsage] = await Promise.all([
        this.checkImageUploadLimit(userId),
        this.checkVoiceRecordingLimit(userId),
      ]);

      return {
        images: imageUsage,
        voice: voiceUsage,
        timeUntilReset: this.getTimeUntilReset(),
        nextResetTime: this.getNextResetTime(),
      };
    } catch (error) {
      console.error('Error getting usage summary:', error);
      throw error;
    }
  }

  /**
   * Record successful image upload
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated usage info
   */
  static async recordImageUpload(userId) {
    try {
      await this.incrementDailyUsage('images', 1);
      return await this.checkImageUploadLimit(userId);
    } catch (error) {
      console.error('Error recording image upload:', error);
      throw error;
    }
  }

  /**
   * Record successful voice memo
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Updated usage info
   */
  static async recordVoiceMemo(userId) {
    try {
      await this.incrementDailyUsage('voice', 1);
      return await this.checkVoiceRecordingLimit(userId);
    } catch (error) {
      console.error('Error recording voice memo:', error);
      throw error;
    }
  }

  /**
   * Clear all usage data (for testing)
   * @returns {Promise<void>}
   */
  static async clearUsageData() {
    try {
      await AsyncStorage.multiRemove([
        this.STORAGE_KEYS.DAILY_IMAGE_COUNT,
        this.STORAGE_KEYS.DAILY_VOICE_COUNT,
        this.STORAGE_KEYS.LAST_RESET_DATE,
      ]);
      console.log('🧹 Usage data cleared');
    } catch (error) {
      console.error('Error clearing usage data:', error);
    }
  }
}

export default UsageTrackingService;
