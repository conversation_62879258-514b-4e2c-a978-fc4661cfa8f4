# ✅ Task Breakdown – Petty Cash Recorder App 

## 🏁 Phase 1: Foundation

- [ ] Set up project structure (React Native + Expo)
- [ ] Define navigation (Home, Settings, Transactions, Reports)
- [ ] Create Issuer Login & Persistent Session as per Login Design Image provided
- [ ] Set up multi-language support (i18n)
  - [ ] Add Hindi, Marathi, Tamil, Bengali
  - [ ] Add language selector in settings
- [ ] Implement Create Cash Box (1 free)
- [ ] Implement Create Custodian (1 free)
- [ ] Link Custodian to Cash Box
- [ ] Expense Items (Predefined dropdown + Other)
- [ ] Add Transaction UI:
  - [ ] Expense category
  - [ ] Amount
  - [ ] Optional voice memo (Paid)
  - [ ] Optional bill photo (Paid)
- [ ] Save Transaction (with timestamp)
- [ ] Transaction list by Cash Box

## 📊 Phase 2: Reporting

- [ ] Weekly report generation (Free)
- [ ] Monthly / Daily / FY Reports (Paid)
- [ ] Reports by Cash Box / Expense Items
- [ ] Graphical Dashboard (Paid)

## ⚙️ Phase 3: Admin & Settings

- [ ] Add/Edit/Delete Cash Boxes (Paid)
- [ ] Add/Edit/Delete Custodians (Paid)
- [ ] Manage Expense Categories per Cash Box (Paid)
- [ ] Settings: Clear Data, Manage Reports, Grant Custodian Access
- [ ] Language selection setting (i18n toggle)
- [ ] Data retention enforcement (3 mo Free, 12 mo Paid)

## 🎁 Phase 4: Premium Features

- [ ] Bill Photo Capture
- [ ] Voice Memo with audio playback (on transaction list)
- [ ] Description Field for transaction
- [ ] Multi-Custodian management
- [ ] Cloud sync / backup (Future)