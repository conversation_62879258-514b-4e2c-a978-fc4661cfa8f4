import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, authHelpers, onAuthStateChange } from '../lib/supabase';
import { getCurrentUserRole, ROLES, canAccessFeature } from '../services/roleService';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();

    // Listen for auth state changes
    const { data: { subscription } } = onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session);

      if (session?.user) {
        setUser(session.user);
        setIsAuthenticated(true);
        // Fetch user role
        await fetchUserRole();
      } else {
        setUser(null);
        setUserRole(null);
        setIsAuthenticated(false);
      }
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const fetchUserRole = async () => {
    try {
      const { role, error } = await getCurrentUserRole();
      if (!error) {
        setUserRole(role);
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
      setUserRole(ROLES.FREE_USER); // Default fallback
    }
  };

  const checkAuthStatus = async () => {
    try {
      const { session, error } = await authHelpers.getSession();

      if (error) {
        console.error('Auth check error:', error);
        setLoading(false);
        return;
      }

      if (session?.user) {
        setUser(session.user);
        setIsAuthenticated(true);
        // Fetch user role
        await fetchUserRole();
      } else {
        setUser(null);
        setUserRole(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Auth check error:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);
      const { data, error } = await authHelpers.signIn(email, password);

      if (error) {
        console.error('Login error:', error);
        return { success: false, error: error.message };
      }

      if (data?.user) {
        setUser(data.user);
        setIsAuthenticated(true);
        // Fetch user role after successful login
        await fetchUserRole();
        return { success: true, error: null };
      }

      return { success: false, error: 'Login failed' };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      const { error } = await authHelpers.signOut();

      if (error) {
        console.error('Logout error:', error);
        return { success: false, error: error.message };
      }

      setUser(null);
      setUserRole(null);
      setIsAuthenticated(false);
      return { success: true, error: null };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const register = async (email, password, userData = {}) => {
    try {
      setLoading(true);
      const { data, error } = await authHelpers.signUp(email, password, userData);

      if (error) {
        console.error('Registration error:', error);
        return { success: false, error: error.message };
      }

      // Note: User might need to confirm email before being fully authenticated
      if (data?.user) {
        return { success: true, error: null, needsConfirmation: !data.session };
      }

      return { success: false, error: 'Registration failed' };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check feature access
  const hasFeatureAccess = (feature) => {
    return canAccessFeature(feature, userRole);
  };

  // Helper function to check if user has specific role
  const hasRole = (role) => {
    return userRole === role;
  };

  // Helper function to check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(userRole);
  };

  const value = {
    isAuthenticated,
    user,
    userRole,
    loading,
    login,
    logout,
    register,
    fetchUserRole,
    hasFeatureAccess,
    hasRole,
    hasAnyRole,
    // Role constants for easy access
    ROLES,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
