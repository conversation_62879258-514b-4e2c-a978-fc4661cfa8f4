import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase, authHelpers, onAuthStateChange } from '../lib/supabase';
import { getCurrentUserRole, ROLES, canAccessFeature } from '../services/roleService';
import { authenticateCustodian, createCustodianSession, validateCustodianSession } from '../services/custodianAuthService';
import { USER_TYPES } from '../utils/loginDetection';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [userType, setUserType] = useState(null); // 'issuer' or 'custodian'
  const [custodianSession, setCustodianSession] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuthStatus();

    // Listen for auth state changes
    const { data: { subscription } } = onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session);

      if (session?.user) {
        setUser(session.user);
        setIsAuthenticated(true);
        // Fetch user role
        await fetchUserRole();
      } else {
        setUser(null);
        setUserRole(null);
        setIsAuthenticated(false);
      }
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => {
      subscription?.unsubscribe();
    };
  }, []);

  const fetchUserRole = async () => {
    try {
      const { role, error } = await getCurrentUserRole();
      if (!error) {
        setUserRole(role);
      }
    } catch (error) {
      console.error('Error fetching user role:', error);
      setUserRole(ROLES.FREE_USER); // Default fallback
    }
  };

  const checkAuthStatus = async () => {
    try {
      // First check for custodian session
      const custodianSessionData = await AsyncStorage.getItem('custodian_session');

      if (custodianSessionData) {
        const session = JSON.parse(custodianSessionData);

        if (validateCustodianSession(session)) {
          // Valid custodian session
          setCustodianSession(session);
          setUser({
            id: session.custodianUserId,
            email: null,
            phone: session.phone,
            user_metadata: {
              full_name: session.fullName,
              phone: session.phone
            }
          });
          setUserType(USER_TYPES.CUSTODIAN);
          setUserRole(ROLES.CUSTODIAN);
          setIsAuthenticated(true);
          setLoading(false);
          return;
        } else {
          // Invalid/expired custodian session
          await AsyncStorage.removeItem('custodian_session');
        }
      }

      // Check for issuer session (Supabase)
      const { session, error } = await authHelpers.getSession();

      if (error) {
        console.error('Auth check error:', error);
        setLoading(false);
        return;
      }

      if (session?.user) {
        setUser(session.user);
        setUserType(USER_TYPES.ISSUER);
        setIsAuthenticated(true);
        // Fetch user role
        await fetchUserRole();
      } else {
        setUser(null);
        setUserRole(null);
        setUserType(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Auth check error:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);
      const { data, error } = await authHelpers.signIn(email, password);

      if (error) {
        console.error('Login error:', error);
        return { success: false, error: error.message };
      }

      if (data?.user) {
        setUser(data.user);
        setUserType(USER_TYPES.ISSUER);
        setIsAuthenticated(true);
        // Fetch user role after successful login
        await fetchUserRole();
        return { success: true, error: null };
      }

      return { success: false, error: 'Login failed' };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const loginCustodian = async (phone, pin) => {
    try {
      // Authenticate custodian
      const authResult = await authenticateCustodian(phone, pin);

      if (!authResult.success) {
        return { success: false, error: authResult.error };
      }

      // Create custodian session
      const sessionResult = await createCustodianSession(authResult.data);

      if (!sessionResult.success) {
        return { success: false, error: sessionResult.error };
      }

      // Set custodian state
      setCustodianSession(sessionResult.session);
      setUser({
        id: authResult.data.custodianUserId,
        email: null,
        phone: authResult.data.phone,
        user_metadata: {
          full_name: authResult.data.fullName,
          phone: authResult.data.phone
        }
      });
      setUserType(USER_TYPES.CUSTODIAN);
      setUserRole(ROLES.CUSTODIAN);
      setIsAuthenticated(true);

      // Store session in AsyncStorage
      await AsyncStorage.setItem('custodian_session', JSON.stringify(sessionResult.session));

      return { success: true, error: null };
    } catch (error) {
      console.error('Custodian login error:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      setLoading(true);

      // Handle different logout types
      if (userType === USER_TYPES.CUSTODIAN) {
        // Custodian logout - clear session
        await AsyncStorage.removeItem('custodian_session');
        setCustodianSession(null);
      } else {
        // Issuer logout - Supabase signout
        const { error } = await authHelpers.signOut();
        if (error) {
          console.error('Logout error:', error);
          return { success: false, error: error.message };
        }
      }

      // Clear all auth state
      setUser(null);
      setUserRole(null);
      setUserType(null);
      setCustodianSession(null);
      setIsAuthenticated(false);
      return { success: true, error: null };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const register = async (email, password, userData = {}) => {
    try {
      setLoading(true);
      const { data, error } = await authHelpers.signUp(email, password, userData);

      if (error) {
        console.error('Registration error:', error);
        return { success: false, error: error.message };
      }

      // Note: User might need to confirm email before being fully authenticated
      if (data?.user) {
        return { success: true, error: null, needsConfirmation: !data.session };
      }

      return { success: false, error: 'Registration failed' };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check feature access
  const hasFeatureAccess = (feature) => {
    return canAccessFeature(feature, userRole);
  };

  // Helper function to check if user has specific role
  const hasRole = (role) => {
    return userRole === role;
  };

  // Helper function to check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(userRole);
  };

  const value = {
    isAuthenticated,
    user,
    userRole,
    userType, // 'issuer' or 'custodian'
    custodianSession,
    loading,
    login,
    loginCustodian, // New custodian login function
    logout,
    register,
    fetchUserRole,
    hasFeatureAccess,
    hasRole,
    hasAnyRole,
    // Role constants for easy access
    ROLES,
    // User type constants
    USER_TYPES,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
