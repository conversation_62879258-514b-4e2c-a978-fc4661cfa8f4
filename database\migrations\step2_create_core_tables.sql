-- =====================================================
-- STEP 2: Create core transaction tables
-- =====================================================
-- Create cash_boxes, expense_categories, transactions tables

-- 1. Create cash_boxes table
CREATE TABLE IF NOT EXISTS cash_boxes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  issuer_user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  initial_amount decimal(10,2) DEFAULT 0,
  current_balance decimal(10,2) DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Ensure unique cash box names per issuer
  UNIQUE(issuer_user_id, name)
);

-- 2. Create expense_categories table
CREATE TABLE IF NOT EXISTS expense_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_box_id uuid REFERENCES cash_boxes(id) ON DELETE CASCADE,
  name text NOT NULL,
  is_predefined boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  
  -- Ensure unique category names per cash box
  UNIQUE(cash_box_id, name)
);

-- 3. Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_box_id uuid NOT NULL REFERENCES cash_boxes(id) ON DELETE CASCADE,
  custodian_user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  expense_category_id uuid NOT NULL REFERENCES expense_categories(id),
  amount decimal(10,2) NOT NULL CHECK (amount > 0),
  description text,
  
  -- Image and voice memo fields (premium features)
  image_url text,
  image_filename text,
  voice_memo_url text,
  voice_memo_filename text,
  
  -- Metadata
  transaction_date timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Ensure positive amounts
  CONSTRAINT positive_amount CHECK (amount > 0)
);

-- 4. Create cash_box_custodians junction table (many-to-many)
CREATE TABLE IF NOT EXISTS cash_box_custodians (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_box_id uuid NOT NULL REFERENCES cash_boxes(id) ON DELETE CASCADE,
  custodian_id uuid NOT NULL REFERENCES custodians(id) ON DELETE CASCADE,
  assigned_at timestamp with time zone DEFAULT now(),
  assigned_by uuid REFERENCES auth.users(id),
  
  -- Ensure unique assignments
  UNIQUE(cash_box_id, custodian_id)
);
