import { supabase } from '../lib/supabase';

/**
 * Role Service - Manages user roles and permissions
 * Provides secure role-based access control
 */

// Role constants
export const ROLES = {
  CUSTODIAN: 'custodian',
  ISSUER: 'issuer',
  ADMIN: 'admin',
  SUPERADMIN: 'superadmin'
};

// Role hierarchy (higher number = more permissions)
export const ROLE_HIERARCHY = {
  [ROLES.CUSTODIAN]: 1,
  [ROLES.ISSUER]: 2,
  [ROLES.ADMIN]: 3,
  [ROLES.SUPERADMIN]: 4
};

// Plan constants
export const PLANS = {
  FREE: 'free',
  STARTER: 'starter',
  PROFESSIONAL: 'professional',
  ENTERPRISE: 'enterprise'
};

/**
 * Get current user's role
 * @returns {Promise<{role: string|null, error: string|null}>}
 */
export const getCurrentUserRole = async () => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { role: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .limit(1);

    if (error) {
      console.error('Error fetching user role:', error);
      return { role: ROLES.ISSUER, error: null }; // Default to issuer
    }

    return { role: data?.[0]?.role || ROLES.ISSUER, error: null };
  } catch (error) {
    console.error('Error in getCurrentUserRole:', error);
    return { role: null, error: error.message };
  }
};

/**
 * Check if current user has required role or higher
 * @param {string} requiredRole - Minimum required role
 * @returns {Promise<boolean>}
 */
export const hasRole = async (requiredRole) => {
  try {
    const { role } = await getCurrentUserRole();
    
    if (!role) return false;
    
    const userLevel = ROLE_HIERARCHY[role] || 0;
    const requiredLevel = ROLE_HIERARCHY[requiredRole] || 0;
    
    return userLevel >= requiredLevel;
  } catch (error) {
    console.error('Error checking role:', error);
    return false;
  }
};

/**
 * Check if current user has any of the specified roles
 * @param {string[]} roles - Array of acceptable roles
 * @returns {Promise<boolean>}
 */
export const hasAnyRole = async (roles) => {
  try {
    const { role } = await getCurrentUserRole();
    return roles.includes(role);
  } catch (error) {
    console.error('Error checking roles:', error);
    return false;
  }
};

/**
 * Get user role by user ID (admin/superadmin only)
 * @param {string} userId - Target user ID
 * @returns {Promise<{role: string|null, error: string|null}>}
 */
export const getUserRole = async (userId) => {
  try {
    // Check if current user is admin or superadmin
    const isAuthorized = await hasAnyRole([ROLES.ADMIN, ROLES.SUPERADMIN]);
    
    if (!isAuthorized) {
      return { role: null, error: 'Unauthorized' };
    }

    const { data, error } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching user role:', error);
      return { role: null, error: error.message };
    }

    return { role: data?.role || ROLES.ISSUER, error: null };
  } catch (error) {
    console.error('Error in getUserRole:', error);
    return { role: null, error: error.message };
  }
};

/**
 * Update user role (superadmin only)
 * @param {string} userId - Target user ID
 * @param {string} newRole - New role to assign
 * @returns {Promise<{success: boolean, error: string|null}>}
 */
export const updateUserRole = async (userId, newRole) => {
  try {
    // Check if current user is superadmin
    const isSuperAdmin = await hasRole(ROLES.SUPERADMIN);
    
    if (!isSuperAdmin) {
      return { success: false, error: 'Only superadmin can update roles' };
    }

    // Validate role
    if (!Object.values(ROLES).includes(newRole)) {
      return { success: false, error: 'Invalid role' };
    }

    const { error } = await supabase
      .from('user_roles')
      .update({ 
        role: newRole,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating user role:', error);
      return { success: false, error: error.message };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in updateUserRole:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all users with their roles (admin/superadmin only)
 * @returns {Promise<{users: Array|null, error: string|null}>}
 */
export const getAllUsersWithRoles = async () => {
  try {
    // Check if current user is admin or superadmin
    const isAuthorized = await hasAnyRole([ROLES.ADMIN, ROLES.SUPERADMIN]);
    
    if (!isAuthorized) {
      return { users: null, error: 'Unauthorized' };
    }

    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        user_id,
        role,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching users with roles:', error);
      return { users: null, error: error.message };
    }

    return { users: data, error: null };
  } catch (error) {
    console.error('Error in getAllUsersWithRoles:', error);
    return { users: null, error: error.message };
  }
};

/**
 * Feature access control based on role
 * @param {string} feature - Feature name
 * @param {string} userRole - User's current role
 * @returns {boolean}
 */
/**
 * Get user's plan information
 * @returns {Promise<{planInfo: object|null, error: string|null}>}
 */
export const getUserPlanInfo = async () => {
  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return { planInfo: null, error: 'User not authenticated' };
    }

    const { data, error } = await supabase.rpc('get_user_plan_info', {
      user_id: user.id
    });

    if (error) {
      console.error('Error fetching plan info:', error);
      return { planInfo: null, error: error.message };
    }

    return { planInfo: data?.[0] || null, error: null };
  } catch (error) {
    console.error('Error in getUserPlanInfo:', error);
    return { planInfo: null, error: error.message };
  }
};

/**
 * Check if user can access feature based on plan
 * @param {string} feature - Feature name
 * @param {object} planInfo - User's plan information
 * @returns {boolean}
 */
export const canAccessFeatureByPlan = (feature, planInfo) => {
  if (!planInfo || !planInfo.features) {
    return false;
  }

  return planInfo.features[feature] === true;
};

/**
 * Legacy feature access (role-based) - keeping for backward compatibility
 * @param {string} feature - Feature name
 * @param {string} userRole - User's role
 * @returns {boolean}
 */
export const canAccessFeature = (feature, userRole) => {
  const featureMatrix = {
    // Basic features (custodians and above)
    'basic_transactions': [ROLES.CUSTODIAN, ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],
    'view_balance': [ROLES.CUSTODIAN, ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],
    'create_cashbox': [ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],

    // Transaction features (available to all users for now)
    'image_upload': [ROLES.CUSTODIAN, ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],
    'description_field': [ROLES.CUSTODIAN, ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],
    'transaction_management': [ROLES.CUSTODIAN, ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],
    'voice_memos': [ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN], // Premium feature

    // Issuer features
    'manage_custodians': [ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],
    'basic_reports': [ROLES.ISSUER, ROLES.ADMIN, ROLES.SUPERADMIN],
    'advanced_reports': [ROLES.ADMIN, ROLES.SUPERADMIN], // Premium feature

    // Admin features
    'user_management': [ROLES.ADMIN, ROLES.SUPERADMIN],
    'analytics_dashboard': [ROLES.ADMIN, ROLES.SUPERADMIN],
    'customer_support': [ROLES.ADMIN, ROLES.SUPERADMIN],
    'view_all_custodians': [ROLES.ADMIN, ROLES.SUPERADMIN],

    // Superadmin features
    'role_management': [ROLES.SUPERADMIN],
    'system_settings': [ROLES.SUPERADMIN],
    'all_user_data': [ROLES.SUPERADMIN],
    'billing_management': [ROLES.SUPERADMIN]
  };

  return featureMatrix[feature]?.includes(userRole) || false;
};

export default {
  ROLES,
  ROLE_HIERARCHY,
  PLANS,
  getCurrentUserRole,
  hasRole,
  hasAnyRole,
  getUserRole,
  updateUserRole,
  getAllUsersWithRoles,
  getUserPlanInfo,
  canAccessFeature,
  canAccessFeatureByPlan
};
