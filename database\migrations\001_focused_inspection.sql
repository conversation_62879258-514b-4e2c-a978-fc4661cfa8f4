-- =====================================================
-- Focused Database Inspection
-- =====================================================
-- Get the critical info we need for the migration

-- 1. MOST IMPORTANT: subscription_plans table structure
SELECT 
    'SUBSCRIPTION_PLANS_STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    COALESCE(column_default, 'NULL') as column_default
FROM information_schema.columns 
WHERE table_name = 'subscription_plans'
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Check specific columns we need
SELECT 
    'COLUMN_EXISTENCE_CHECK' as section,
    'plan_code' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'plan_code'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default

UNION ALL

SELECT 
    'COLUMN_EXISTENCE_CHECK' as section,
    'cash_box_limit' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'cash_box_limit'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default

UNION ALL

SELECT 
    'COLUMN_EXISTENCE_CHECK' as section,
    'daily_image_limit' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'daily_image_limit'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default

UNION ALL

SELECT 
    'COLUMN_EXISTENCE_CHECK' as section,
    'features' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'features'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default;

-- 3. Show existing data in subscription_plans
SELECT 
    'EXISTING_PLANS_DATA' as section,
    COALESCE(plan_name, 'NULL') as column_name,
    COALESCE(custodian_limit::text, 'NULL') as data_type,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'plan_code'
    ) THEN COALESCE(plan_code, 'NULL') ELSE 'NO_PLAN_CODE_COLUMN' END as is_nullable,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'price_monthly'
    ) THEN COALESCE(price_monthly::text, 'NULL') ELSE 'NO_PRICE_COLUMN' END as column_default
FROM subscription_plans;

-- 4. Check user_roles structure (key columns only)
SELECT 
    'USER_ROLES_STRUCTURE' as section,
    column_name,
    data_type,
    is_nullable,
    COALESCE(column_default, 'NULL') as column_default
FROM information_schema.columns 
WHERE table_name = 'user_roles'
  AND table_schema = 'public'
  AND column_name IN ('id', 'user_id', 'role', 'subscription_plan_id', 'preferred_language')
ORDER BY ordinal_position;

-- 5. Check what other transaction-related tables exist
SELECT 
    'TRANSACTION_TABLES_CHECK' as section,
    table_name as column_name,
    'EXISTS' as data_type,
    '' as is_nullable,
    '' as column_default
FROM information_schema.tables 
WHERE table_schema = 'public'
  AND table_name IN ('cash_boxes', 'transactions', 'expense_categories', 'cash_box_custodians')
ORDER BY table_name;
