import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useFocusEffect } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';
import ImagePreview from '../../components/ImagePreview';
import { TransactionService } from '../../services/transactionService';
import { useAuth } from '../../context/AuthContext';
import { testImageAccess } from '../../utils/setupStorage';

export default function TransactionsScreen({ navigation }) {
  const { t } = useTranslation();
  const { hasFeatureAccess } = useAuth();
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [cashBoxes, setCashBoxes] = useState([]);
  const [selectedCashBox, setSelectedCashBox] = useState(null);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (selectedCashBox) {
      loadTransactions(selectedCashBox);
    }
  }, [selectedCashBox]);

  // Auto-refresh when screen comes into focus (user returns from AddTransaction)
  useFocusEffect(
    React.useCallback(() => {
      if (selectedCashBox) {
        console.log('🔄 Screen focused - refreshing transactions');
        loadTransactions(selectedCashBox);
      }
    }, [selectedCashBox])
  );

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Load cash boxes first
      const boxes = await TransactionService.getCashBoxes();
      setCashBoxes(boxes);

      // Select first cash box by default
      if (boxes.length > 0) {
        setSelectedCashBox(boxes[0].id);
      } else {
        setTransactions([]);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      Alert.alert('Error', 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async (cashBoxId = null) => {
    if (!loading) setLoading(true);
    try {
      const data = await TransactionService.getTransactions(cashBoxId, 50, 0);
      setTransactions(data);
    } catch (error) {
      console.error('Error loading transactions:', error);
      Alert.alert('Error', 'Failed to load transactions');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadTransactions(selectedCashBox);
    setRefreshing(false);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatAmount = (amount) => {
    return `₹${parseFloat(amount).toFixed(2)}`;
  };

  const handleDeleteTransaction = async (transactionId) => {
    Alert.alert(
      'Delete Transaction',
      'Are you sure you want to delete this transaction?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await TransactionService.deleteTransaction(transactionId);
              if (result.success) {
                // Refresh transactions
                await loadTransactions(selectedCashBox);
                Alert.alert('Success', 'Transaction deleted successfully');
              } else {
                Alert.alert('Error', result.error);
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to delete transaction');
            }
          },
        },
      ]
    );
  };

  const renderTransaction = ({ item }) => {
    // Debug logging for image URL (reduced)
    if (item.image_url) {
      console.log('🖼️ Transaction has image_url');
    }

    return (
      <TouchableOpacity style={styles.transactionCard}>
        <View style={styles.transactionHeader}>
          <View style={styles.categoryContainer}>
            <Icon name="receipt" size={20} color={colors.primary} />
            <Text style={styles.category}>{item.category_name}</Text>
          </View>
          <View style={styles.amountContainer}>
            <Text style={styles.amount}>{formatAmount(item.amount)}</Text>
            {hasFeatureAccess('transaction_management') && (
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={() => handleDeleteTransaction(item.id)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Icon name="delete" size={16} color={colors.error} />
              </TouchableOpacity>
            )}
          </View>
        </View>

      {/* Image and Description Row */}
      {(item.image_url || item.description) && (
        <View style={styles.contentRow}>
          {/* Image preview if available */}
          {item.image_url && (
            <View style={styles.imageContainer}>
              <ImagePreview
                imageUrl={item.image_url}
                thumbnailSize={60}
                showDeleteButton={false}
                showFullscreenButton={true}
              />
            </View>
          )}

          {/* Description next to image */}
          {item.description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.description}>{item.description}</Text>
            </View>
          )}
        </View>
      )}

      <View style={styles.transactionFooter}>
        <Text style={styles.cashBox}>{item.cash_box_name}</Text>
        <Text style={styles.date}>{formatDate(item.transaction_date)}</Text>
      </View>

      {item.custodian_name && (
        <Text style={styles.custodianName}>By: {item.custodian_name}</Text>
      )}
    </TouchableOpacity>
    );
  };

  const renderEmptyState = () => {
    if (cashBoxes.length === 0) {
      return (
        <View style={styles.emptyState}>
          <Icon name="account-balance-wallet" size={64} color={colors.placeholder} />
          <Text style={styles.emptyTitle}>No Cash Boxes</Text>
          <Text style={styles.emptySubtitle}>
            Create a cash box first to start recording transactions
          </Text>
          <TouchableOpacity
            style={globalStyles.primaryButton}
            onPress={() => navigation.navigate('CreateCashBox')}
          >
            <Text style={globalStyles.buttonText}>Create Cash Box</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return (
      <View style={styles.emptyState}>
        <Icon name="receipt-long" size={64} color={colors.placeholder} />
        <Text style={styles.emptyTitle}>No Transactions Yet</Text>
        <Text style={styles.emptySubtitle}>
          Start recording your petty cash transactions
        </Text>
        <TouchableOpacity
          style={globalStyles.primaryButton}
          onPress={() => navigation.navigate('AddTransaction', { cashBoxId: selectedCashBox })}
        >
          <Text style={globalStyles.buttonText}>Add First Transaction</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const getCashBoxName = (id) => {
    const box = cashBoxes.find(b => b.id === id);
    return box ? box.name : 'All Cash Boxes';
  };

  const showDeleteOptions = () => {
    Alert.alert(
      'Cash Box Management',
      `What would you like to do with "${getCashBoxName(selectedCashBox)}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete All Transactions',
          style: 'destructive',
          onPress: () => confirmDeleteAllTransactions(),
        },
        {
          text: 'Delete Entire Cash Box',
          style: 'destructive',
          onPress: () => confirmDeleteCashBox(),
        },
      ]
    );
  };

  const confirmDeleteAllTransactions = () => {
    Alert.alert(
      'Delete All Transactions',
      `Are you sure you want to delete ALL transactions in "${getCashBoxName(selectedCashBox)}"?\n\nThis will:\n• Delete all transactions\n• Delete all images\n• Reset balance to initial amount\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete All Transactions',
          style: 'destructive',
          onPress: () => handleDeleteAllTransactions(),
        },
      ]
    );
  };

  const confirmDeleteCashBox = () => {
    Alert.alert(
      'Delete Cash Box',
      `Are you sure you want to delete "${getCashBoxName(selectedCashBox)}" completely?\n\nThis will:\n• Delete the cash box\n• Delete all transactions\n• Delete all images\n• Delete all categories\n\nThis action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Cash Box',
          style: 'destructive',
          onPress: () => handleDeleteCashBox(),
        },
      ]
    );
  };

  const handleDeleteAllTransactions = async () => {
    try {
      setLoading(true);
      const result = await TransactionService.deleteAllTransactions(selectedCashBox);

      if (result.success) {
        Alert.alert('Success', result.message);
        // Refresh transactions
        await loadTransactions(selectedCashBox);
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to delete transactions');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCashBox = async () => {
    try {
      setLoading(true);
      const result = await TransactionService.deleteCashBox(selectedCashBox);

      if (result.success) {
        Alert.alert('Success', result.message);
        // Reload initial data (cash boxes list)
        await loadInitialData();
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to delete cash box');
    } finally {
      setLoading(false);
    }
  };

  if (loading && transactions.length === 0) {
    return (
      <View style={styles.container}>
        <AppHeader />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading transactions...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Global Header */}
      <AppHeader />

      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Transactions</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => navigation.navigate('AddTransaction', { cashBoxId: selectedCashBox })}
            disabled={!selectedCashBox}
          >
            <Icon name="add" size={24} color={colors.background} />
          </TouchableOpacity>
        </View>

        {/* Cash Box Filter & Management */}
        {cashBoxes.length > 0 && (
          <View style={styles.filterContainer}>
            <Text style={styles.filterLabel}>Cash Box:</Text>
            <View style={styles.filterRow}>
              {cashBoxes.length > 1 && (
                <TouchableOpacity
                  style={styles.filterButton}
                  onPress={() => {
                    // Simple toggle between cash boxes for now
                    const currentIndex = cashBoxes.findIndex(b => b.id === selectedCashBox);
                    const nextIndex = (currentIndex + 1) % cashBoxes.length;
                    setSelectedCashBox(cashBoxes[nextIndex].id);
                  }}
                >
                  <Text style={styles.filterText}>{getCashBoxName(selectedCashBox)}</Text>
                  <Icon name="swap-horiz" size={20} color={colors.primary} />
                </TouchableOpacity>
              )}

              {cashBoxes.length === 1 && (
                <Text style={styles.singleCashBoxText}>{getCashBoxName(selectedCashBox)}</Text>
              )}

              {/* Delete Options */}
              <TouchableOpacity
                style={styles.deleteOptionsButton}
                onPress={() => showDeleteOptions()}
              >
                <Icon name="more-vert" size={20} color={colors.text} />
              </TouchableOpacity>
            </View>
          </View>
        )}

        <FlatList
          data={transactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          contentContainerStyle={transactions.length === 0 ? styles.emptyContainer : styles.listContainer}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.text,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
  },
  addButton: {
    backgroundColor: colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.onSurface,
    marginBottom: 8,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    flex: 1,
    marginRight: 8,
  },
  filterText: {
    fontSize: 14,
    color: colors.text,
    marginRight: 8,
    flex: 1,
  },
  singleCashBoxText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
  },
  deleteOptionsButton: {
    padding: 8,
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 27, // Global 27px margin bottom
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  transactionCard: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  category: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text, // Global text color
    marginLeft: 8,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  amount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  deleteButton: {
    padding: 4,
  },
  contentRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    gap: 12,
  },
  imageContainer: {
    // Remove marginBottom since it's now in a row
  },
  descriptionContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  description: {
    fontSize: 14,
    color: colors.text, // Global text color
    fontStyle: 'italic',
    lineHeight: 20,
  },
  transactionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  cashBox: {
    fontSize: 12,
    color: colors.text, // Global text color
    fontWeight: '500',
  },
  date: {
    fontSize: 12,
    color: colors.placeholder,
  },
  custodianName: {
    fontSize: 12,
    color: colors.placeholder,
    marginTop: 4,
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: colors.text, // Global text color
    textAlign: 'center',
    marginBottom: 24,
  },
});
