import { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useFocusEffect } from '@react-navigation/native';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';
import { useAuth } from '../../context/AuthContext';
import { 
  getCustodians, 
  getPlanInfo, 
  toggleCustodianStatus, 
  formatPhoneNumber,
  getPlanDisplayInfo 
} from '../../services/custodianService';

export default function ManageCustodiansScreen({ navigation }) {
  const { hasFeatureAccess } = useAuth();
  const [custodians, setCustodians] = useState([]);
  const [planInfo, setPlanInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Check if user can manage custodians
  if (!hasFeatureAccess('manage_custodians')) {
    return (
      <View style={styles.container}>
        <AppHeader />
        <View style={[globalStyles.screenContainer, styles.errorContainer]}>
          <Icon name="block" size={48} color={colors.error} />
          <Text style={styles.errorText}>Access Denied</Text>
          <Text style={styles.errorSubtext}>You don't have permission to manage custodians</Text>
        </View>
      </View>
    );
  }

  const fetchData = async () => {
    try {
      console.log('🔄 Fetching custodians and plan info...');
      const [custodiansResult, planResult] = await Promise.all([
        getCustodians(),
        getPlanInfo()
      ]);

      console.log('📊 Plan result:', planResult);
      console.log('👥 Custodians result:', custodiansResult);

      if (custodiansResult.success) {
        setCustodians(custodiansResult.data);
      } else {
        Alert.alert('Error', custodiansResult.error);
      }

      if (planResult.success) {
        console.log('✅ Setting plan info:', planResult.data);
        setPlanInfo(planResult.data);
      } else {
        console.warn('❌ Failed to fetch plan info:', planResult.error);
      }
    } catch (error) {
      console.error('💥 Error fetching data:', error);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [])
  );

  const handleRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  const handleAddCustodian = () => {
    console.log('🎯 handleAddCustodian called, planInfo:', planInfo);
    if (!planInfo) {
      Alert.alert(
        'Plan Information Unavailable',
        'Unable to load your subscription plan. This might be a temporary issue. Would you like to try again?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => fetchData() }
        ]
      );
      return;
    }

    if (planInfo.custodians_remaining <= 0) {
      const planDisplay = getPlanDisplayInfo(planInfo.plan_code);
      Alert.alert(
        'Custodian Limit Reached',
        `Your ${planDisplay.name} allows only ${planInfo.custodian_limit} custodian(s). Upgrade your plan for more custodians.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Upgrade Plan', onPress: () => handleUpgradePlan() }
        ]
      );
      return;
    }

    navigation.navigate('AddCustodian');
  };

  const handleUpgradePlan = () => {
    Alert.alert('Upgrade Plan', 'Plan upgrade functionality coming soon!');
  };

  const handleToggleStatus = async (custodian) => {
    const newStatus = !custodian.is_active;
    const action = newStatus ? 'activate' : 'deactivate';
    
    Alert.alert(
      `${action.charAt(0).toUpperCase() + action.slice(1)} Custodian`,
      `Are you sure you want to ${action} ${custodian.full_name}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: action.charAt(0).toUpperCase() + action.slice(1),
          onPress: async () => {
            const result = await toggleCustodianStatus(custodian.id, newStatus);
            if (result.success) {
              fetchData(); // Refresh the list
            } else {
              Alert.alert('Error', result.error);
            }
          }
        }
      ]
    );
  };

  const renderCustodianItem = ({ item }) => (
    <View style={styles.custodianCard}>
      <View style={styles.custodianInfo}>
        <View style={styles.custodianHeader}>
          <Text style={styles.custodianName}>{item.full_name}</Text>
          <View style={[styles.statusBadge, item.is_active ? styles.activeBadge : styles.inactiveBadge]}>
            <Text style={[styles.statusText, item.is_active ? styles.activeText : styles.inactiveText]}>
              {item.is_active ? 'Active' : 'Inactive'}
            </Text>
          </View>
        </View>
        
        <View style={styles.custodianDetails}>
          <Icon name="phone" size={16} color={colors.text} />
          <Text style={styles.phoneText}>{formatPhoneNumber(item.phone_number)}</Text>
        </View>
        
        <Text style={styles.createdDate}>
          Created: {new Date(item.created_at).toLocaleDateString()}
        </Text>
      </View>

      <View style={styles.custodianActions}>
        <TouchableOpacity
          style={[styles.actionButton, item.is_active ? styles.deactivateButton : styles.activateButton]}
          onPress={() => handleToggleStatus(item)}
        >
          <Icon 
            name={item.is_active ? 'pause' : 'play_arrow'} 
            size={20} 
            color={colors.background} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPlanInfo = () => {
    if (!planInfo) return null;

    const planDisplay = getPlanDisplayInfo(planInfo.plan_code);
    
    return (
      <View style={styles.planCard}>
        <View style={styles.planHeader}>
          <Text style={styles.planTitle}>
            {planDisplay.emoji} {planDisplay.name}
          </Text>
          {planInfo.plan_code !== 'enterprise' && (
            <TouchableOpacity style={styles.upgradeButton} onPress={handleUpgradePlan}>
              <Text style={styles.upgradeButtonText}>Upgrade</Text>
            </TouchableOpacity>
          )}
        </View>
        
        <View style={styles.usageInfo}>
          <Text style={styles.usageText}>
            Custodians: {planInfo.custodians_used}/{planInfo.custodian_limit}
          </Text>
          <View style={styles.usageBar}>
            <View 
              style={[
                styles.usageProgress, 
                { 
                  width: `${(planInfo.custodians_used / planInfo.custodian_limit) * 100}%`,
                  backgroundColor: planInfo.custodians_remaining > 0 ? colors.primary : colors.error
                }
              ]} 
            />
          </View>
        </View>
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Icon name="people" size={64} color={colors.placeholder} />
      <Text style={styles.emptyTitle}>No Custodians Yet</Text>
      <Text style={styles.emptySubtext}>
        Add custodians to help manage your petty cash transactions
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddCustodian}>
        <Icon name="add" size={24} color={colors.background} />
        <Text style={styles.addButtonText}>Add First Custodian</Text>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <AppHeader />
        <View style={[globalStyles.screenContainer, styles.loadingContainer]}>
          <Text style={styles.loadingText}>Loading custodians...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AppHeader />
      
      <View style={globalStyles.screenContainer}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Manage Custodians</Text>
          <TouchableOpacity style={styles.addIconButton} onPress={handleAddCustodian}>
            <Icon name="add" size={24} color={colors.background} />
          </TouchableOpacity>
        </View>

        {renderPlanInfo()}

        <FlatList
          data={custodians}
          renderItem={renderCustodianItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={custodians.length === 0 ? styles.emptyList : styles.list}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
  },
  addIconButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    padding: 8,
  },
  planCard: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  planTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
  },
  upgradeButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  upgradeButtonText: {
    color: colors.background,
    fontSize: 12,
    fontWeight: 'bold',
  },
  usageInfo: {
    marginTop: 8,
  },
  usageText: {
    fontSize: 14,
    color: colors.text, // Global text color
    marginBottom: 8,
  },
  usageBar: {
    height: 6,
    backgroundColor: colors.border,
    borderRadius: 3,
    overflow: 'hidden',
  },
  usageProgress: {
    height: '100%',
    borderRadius: 3,
  },
  list: {
    paddingHorizontal: 16,
  },
  emptyList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  custodianCard: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: colors.border,
  },
  custodianInfo: {
    flex: 1,
  },
  custodianHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  custodianName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4, // Global 4px border radius
  },
  activeBadge: {
    backgroundColor: colors.success + '20',
  },
  inactiveBadge: {
    backgroundColor: colors.error + '20',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  activeText: {
    color: colors.success,
  },
  inactiveText: {
    color: colors.error,
  },
  custodianDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  phoneText: {
    fontSize: 14,
    color: colors.text, // Global text color
    marginLeft: 8,
  },
  createdDate: {
    fontSize: 12,
    color: colors.text, // Global text color
    opacity: 0.7,
  },
  custodianActions: {
    justifyContent: 'center',
    marginLeft: 12,
  },
  actionButton: {
    borderRadius: 4, // Global 4px border radius
    padding: 8,
  },
  activateButton: {
    backgroundColor: colors.success,
  },
  deactivateButton: {
    backgroundColor: colors.error,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: colors.text, // Global text color
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 32,
  },
  addButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    paddingHorizontal: 24,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  addButtonText: {
    color: colors.background,
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: colors.text, // Global text color
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.error,
    marginTop: 16,
  },
  errorSubtext: {
    fontSize: 14,
    color: colors.text, // Global text color
    textAlign: 'center',
    marginTop: 8,
  },
});
