import { supabase } from '../lib/supabase';
import { StorageService } from './storageService';

/**
 * Transaction Management Service
 * Handles CRUD operations for transactions, cash boxes, and expense categories
 */

export class TransactionService {
  
  /**
   * Create a new cash box with predefined categories
   * @param {string} name - Cash box name
   * @param {string} description - Optional description
   * @param {number} initialAmount - Initial amount (default: 0)
   * @returns {Promise<Object>} Result with cash box data
   */
  static async createCashBox(name, description = null, initialAmount = 0) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase.rpc('create_cash_box_with_categories', {
        issuer_id: user.id,
        box_name: name.trim(),
        box_description: description?.trim() || null,
        initial_amt: initialAmount
      });

      if (error) {
        console.error('Error creating cash box:', error);
        throw new Error(error.message);
      }

      console.log('✅ Cash box created successfully:', data);
      return { success: true, cashBoxId: data };

    } catch (error) {
      console.error('❌ Error creating cash box:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get all cash boxes for current user
   * @returns {Promise<Array>} Array of cash boxes
   */
  static async getCashBoxes() {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('cash_boxes')
        .select('*')
        .eq('issuer_user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) {
        throw new Error(error.message);
      }

      return data || [];

    } catch (error) {
      console.error('❌ Error fetching cash boxes:', error);
      throw error;
    }
  }

  /**
   * Get expense categories for a cash box
   * @param {string} cashBoxId - Cash box ID
   * @returns {Promise<Array>} Array of expense categories
   */
  static async getExpenseCategories(cashBoxId) {
    try {
      const { data, error } = await supabase
        .from('expense_categories')
        .select('*')
        .eq('cash_box_id', cashBoxId)
        .eq('is_active', true)
        .order('is_predefined', { ascending: false })
        .order('name', { ascending: true });

      if (error) {
        throw new Error(error.message);
      }

      return data || [];

    } catch (error) {
      console.error('❌ Error fetching expense categories:', error);
      throw error;
    }
  }

  /**
   * Create a new transaction
   * @param {Object} transactionData - Transaction details
   * @returns {Promise<Object>} Result with transaction data
   */
  static async createTransaction(transactionData) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      const {
        cashBoxId,
        expenseCategoryId,
        amount,
        description,
        imageUri,
        voiceMemoUri
      } = transactionData;

      let imageUrl = null;
      let imageFilename = null;
      let voiceMemoUrl = null;
      let voiceMemoFilename = null;

      // Upload image if provided
      if (imageUri) {
        console.log('📸 Uploading transaction image...');
        const uploadResult = await StorageService.uploadTransactionImageWithProgress(
          imageUri,
          user.id,
          (progress, message) => {
            console.log(`📊 Upload progress: ${progress}% - ${message}`);
          }
        );

        if (uploadResult.success) {
          imageUrl = uploadResult.publicUrl;
          imageFilename = uploadResult.fileName;
          console.log('✅ Image uploaded successfully');
        } else {
          throw new Error('Failed to upload image');
        }
      }

      // Upload voice memo if provided (future feature)
      if (voiceMemoUri) {
        // TODO: Implement voice memo upload
        console.log('🎤 Voice memo upload not yet implemented');
      }

      // Create transaction using database function
      const { data, error } = await supabase.rpc('create_transaction', {
        p_cash_box_id: cashBoxId,
        p_custodian_user_id: user.id,
        p_expense_category_id: expenseCategoryId,
        p_amount: parseFloat(amount),
        p_description: description?.trim() || null,
        p_image_url: imageUrl,
        p_image_filename: imageFilename,
        p_voice_memo_url: voiceMemoUrl,
        p_voice_memo_filename: voiceMemoFilename
      });

      if (error) {
        console.error('Error creating transaction:', error);
        
        // If transaction creation failed but image was uploaded, clean up
        if (imageFilename) {
          await StorageService.deleteTransactionImage(imageFilename);
        }
        
        throw new Error(error.message);
      }

      console.log('✅ Transaction created successfully:', data);
      return { success: true, transactionId: data };

    } catch (error) {
      console.error('❌ Error creating transaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get transactions for a cash box
   * @param {string} cashBoxId - Cash box ID (optional, gets all if not provided)
   * @param {number} limit - Number of transactions to fetch (default: 50)
   * @param {number} offset - Offset for pagination (default: 0)
   * @returns {Promise<Array>} Array of transactions
   */
  static async getTransactions(cashBoxId = null, limit = 50, offset = 0) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      let query = supabase
        .from('transaction_details')
        .select('*')
        .order('transaction_date', { ascending: false })
        .range(offset, offset + limit - 1);

      // Filter by cash box if provided
      if (cashBoxId) {
        query = query.eq('cash_box_id', cashBoxId);
      }

      // Filter by user's cash boxes (for issuers) or user's own transactions (for custodians)
      query = query.or(`issuer_user_id.eq.${user.id},custodian_user_id.eq.${user.id}`);

      const { data, error } = await query;

      if (error) {
        throw new Error(error.message);
      }

      return data || [];

    } catch (error) {
      console.error('❌ Error fetching transactions:', error);
      throw error;
    }
  }

  /**
   * Delete a transaction
   * @param {string} transactionId - Transaction ID
   * @returns {Promise<Object>} Result
   */
  static async deleteTransaction(transactionId) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // First get the transaction to check permissions and get file info
      const { data: transaction, error: fetchError } = await supabase
        .from('transactions')
        .select('*, cash_boxes!inner(issuer_user_id)')
        .eq('id', transactionId)
        .single();

      if (fetchError) {
        throw new Error('Transaction not found');
      }

      // Check if user has permission to delete (issuer or the custodian who created it)
      const canDelete = transaction.cash_boxes.issuer_user_id === user.id || 
                       transaction.custodian_user_id === user.id;

      if (!canDelete) {
        throw new Error('Permission denied');
      }

      // Delete associated files first
      if (transaction.image_filename) {
        await StorageService.deleteTransactionImage(transaction.image_filename);
      }

      // Delete the transaction
      const { error: deleteError } = await supabase
        .from('transactions')
        .delete()
        .eq('id', transactionId);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      console.log('✅ Transaction deleted successfully');
      return { success: true };

    } catch (error) {
      console.error('❌ Error deleting transaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get cash box summary (balance, transaction count, etc.)
   * @param {string} cashBoxId - Cash box ID
   * @returns {Promise<Object>} Cash box summary
   */
  static async getCashBoxSummary(cashBoxId) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();
      
      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      // Get cash box details
      const { data: cashBox, error: boxError } = await supabase
        .from('cash_boxes')
        .select('*')
        .eq('id', cashBoxId)
        .eq('issuer_user_id', user.id)
        .single();

      if (boxError) {
        throw new Error('Cash box not found');
      }

      // Get transaction count and total spent
      const { data: stats, error: statsError } = await supabase
        .from('transactions')
        .select('amount')
        .eq('cash_box_id', cashBoxId);

      if (statsError) {
        throw new Error(statsError.message);
      }

      const transactionCount = stats.length;
      const totalSpent = stats.reduce((sum, t) => sum + parseFloat(t.amount), 0);

      return {
        ...cashBox,
        transactionCount,
        totalSpent,
        remainingBalance: cashBox.current_balance
      };

    } catch (error) {
      console.error('❌ Error fetching cash box summary:', error);
      throw error;
    }
  }

  /**
   * Add custom expense category
   * @param {string} cashBoxId - Cash box ID
   * @param {string} categoryName - Category name
   * @returns {Promise<Object>} Result
   */
  static async addExpenseCategory(cashBoxId, categoryName) {
    try {
      const { data, error } = await supabase
        .from('expense_categories')
        .insert({
          cash_box_id: cashBoxId,
          name: categoryName.trim(),
          is_predefined: false
        })
        .select()
        .single();

      if (error) {
        throw new Error(error.message);
      }

      console.log('✅ Expense category added successfully');
      return { success: true, category: data };

    } catch (error) {
      console.error('❌ Error adding expense category:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Clean up orphaned images (images without associated transactions)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Cleanup result
   */
  static async cleanupOrphanedImages(userId) {
    try {
      console.log('🧹 Starting orphaned image cleanup...');

      // Get all transactions with images for this user
      const { data: transactions, error: transError } = await supabase
        .from('transactions')
        .select('image_filename, cash_boxes!inner(issuer_user_id)')
        .not('image_filename', 'is', null)
        .eq('cash_boxes.issuer_user_id', userId);

      if (transError) {
        throw new Error(transError.message);
      }

      // Get list of all images in storage for this user
      const { data: storageFiles, error: storageError } = await supabase.storage
        .from(StorageService.BUCKETS.TRANSACTION_IMAGES)
        .list(`${userId}/transaction`);

      if (storageError) {
        throw new Error(storageError.message);
      }

      // Find orphaned files
      const usedFilenames = new Set(transactions.map(t => t.image_filename.split('/').pop()));
      const orphanedFiles = storageFiles
        .filter(file => !usedFilenames.has(file.name))
        .map(file => `${userId}/transaction/${file.name}`);

      if (orphanedFiles.length === 0) {
        console.log('✅ No orphaned images found');
        return { success: true, deletedCount: 0, message: 'No orphaned images found' };
      }

      // Delete orphaned files
      const deleteResult = await StorageService.deleteMultipleImages(orphanedFiles);

      console.log(`🧹 Cleanup complete: ${deleteResult.deletedCount} orphaned images removed`);

      return {
        success: true,
        deletedCount: deleteResult.deletedCount,
        failedCount: deleteResult.failedCount,
        message: `Cleaned up ${deleteResult.deletedCount} orphaned images`
      };

    } catch (error) {
      console.error('❌ Error during image cleanup:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get storage usage statistics for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Storage statistics
   */
  static async getStorageStats(userId) {
    try {
      // Get transaction count with images
      const { data: transactions, error: transError } = await supabase
        .from('transactions')
        .select('image_filename, cash_boxes!inner(issuer_user_id)')
        .not('image_filename', 'is', null)
        .eq('cash_boxes.issuer_user_id', userId);

      if (transError) {
        throw new Error(transError.message);
      }

      // Get storage files
      const { data: storageFiles, error: storageError } = await supabase.storage
        .from(StorageService.BUCKETS.TRANSACTION_IMAGES)
        .list(`${userId}/transaction`);

      if (storageError) {
        throw new Error(storageError.message);
      }

      const totalFiles = storageFiles.length;
      const totalSize = storageFiles.reduce((sum, file) => sum + (file.metadata?.size || 0), 0);
      const transactionImages = transactions.length;
      const orphanedImages = totalFiles - transactionImages;

      return {
        success: true,
        stats: {
          totalFiles,
          totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
          transactionImages,
          orphanedImages,
          storageEfficiency: totalFiles > 0 ? ((transactionImages / totalFiles) * 100).toFixed(1) : 100
        }
      };

    } catch (error) {
      console.error('❌ Error getting storage stats:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete a cash box and all its associated data
   * @param {string} cashBoxId - Cash box ID
   * @returns {Promise<Object>} Result
   */
  static async deleteCashBox(cashBoxId) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      console.log('🗑️ Starting cash box deletion process...');

      // First, get all transactions with images for this cash box
      const { data: transactions, error: transError } = await supabase
        .from('transactions')
        .select('id, image_filename')
        .eq('cash_box_id', cashBoxId);

      if (transError) {
        throw new Error(transError.message);
      }

      // Collect all image filenames for deletion
      const imageFilenames = transactions
        .filter(t => t.image_filename)
        .map(t => t.image_filename);

      console.log(`📊 Found ${transactions.length} transactions, ${imageFilenames.length} with images`);

      // Delete all images from storage
      if (imageFilenames.length > 0) {
        console.log('🖼️ Deleting transaction images...');
        const deleteResult = await StorageService.deleteMultipleImages(imageFilenames);
        console.log(`🗑️ Deleted ${deleteResult.deletedCount} images, ${deleteResult.failedCount} failed`);
      }

      // Delete the cash box (this will cascade delete transactions and categories due to foreign keys)
      const { error: deleteError } = await supabase
        .from('cash_boxes')
        .delete()
        .eq('id', cashBoxId)
        .eq('issuer_user_id', user.id); // Ensure user owns this cash box

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      console.log('✅ Cash box and all associated data deleted successfully');

      return {
        success: true,
        message: `Cash box deleted successfully. Removed ${transactions.length} transactions and ${imageFilenames.length} images.`
      };

    } catch (error) {
      console.error('❌ Error deleting cash box:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete all transactions for a cash box (but keep the cash box)
   * @param {string} cashBoxId - Cash box ID
   * @returns {Promise<Object>} Result
   */
  static async deleteAllTransactions(cashBoxId) {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        throw new Error('User not authenticated');
      }

      console.log('🗑️ Starting transaction deletion process...');

      // Get all transactions with images for this cash box
      const { data: transactions, error: transError } = await supabase
        .from('transactions')
        .select('id, image_filename, cash_boxes!inner(issuer_user_id)')
        .eq('cash_box_id', cashBoxId)
        .eq('cash_boxes.issuer_user_id', user.id);

      if (transError) {
        throw new Error(transError.message);
      }

      // Collect all image filenames for deletion
      const imageFilenames = transactions
        .filter(t => t.image_filename)
        .map(t => t.image_filename);

      console.log(`📊 Found ${transactions.length} transactions, ${imageFilenames.length} with images`);

      // Delete all images from storage
      if (imageFilenames.length > 0) {
        console.log('🖼️ Deleting transaction images...');
        const deleteResult = await StorageService.deleteMultipleImages(imageFilenames);
        console.log(`🗑️ Deleted ${deleteResult.deletedCount} images, ${deleteResult.failedCount} failed`);
      }

      // Delete all transactions for this cash box
      const { error: deleteError } = await supabase
        .from('transactions')
        .delete()
        .eq('cash_box_id', cashBoxId);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      // Reset cash box balance to initial amount
      const { error: resetError } = await supabase
        .from('cash_boxes')
        .update({
          current_balance: supabase.raw('initial_amount'),
          updated_at: new Date().toISOString()
        })
        .eq('id', cashBoxId)
        .eq('issuer_user_id', user.id);

      if (resetError) {
        console.warn('Warning: Could not reset cash box balance:', resetError);
      }

      console.log('✅ All transactions deleted successfully');

      return {
        success: true,
        message: `All transactions deleted successfully. Removed ${transactions.length} transactions and ${imageFilenames.length} images.`
      };

    } catch (error) {
      console.error('❌ Error deleting transactions:', error);
      return { success: false, error: error.message };
    }
  }
}
