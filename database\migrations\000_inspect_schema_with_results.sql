-- =====================================================
-- Database Schema Inspector (Returns Result Sets)
-- =====================================================
-- This version returns actual data you can see

-- 1. Check if subscription_plans table exists and show its structure
SELECT 
    'subscription_plans_columns' as info_type,
    column_name,
    data_type,
    is_nullable,
    column_default,
    '' as constraint_info
FROM information_schema.columns 
WHERE table_name = 'subscription_plans'
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 2. Show subscription_plans constraints
SELECT 
    'subscription_plans_constraints' as info_type,
    constraint_name as column_name,
    constraint_type as data_type,
    '' as is_nullable,
    '' as column_default,
    constraint_type as constraint_info
FROM information_schema.table_constraints 
WHERE table_name = 'subscription_plans'
  AND table_schema = 'public';

-- 3. Show existing subscription_plans data (if table exists)
SELECT 
    'subscription_plans_data' as info_type,
    COALESCE(plan_name, 'N/A') as column_name,
    COALESCE(custodian_limit::text, 'N/A') as data_type,
    COALESCE(plan_code, 'N/A') as is_nullable,
    COALESCE(price_monthly::text, 'N/A') as column_default,
    'existing_data' as constraint_info
FROM subscription_plans
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'subscription_plans');

-- 4. Check user_roles table structure
SELECT 
    'user_roles_columns' as info_type,
    column_name,
    data_type,
    is_nullable,
    column_default,
    '' as constraint_info
FROM information_schema.columns 
WHERE table_name = 'user_roles'
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 5. Check custodians table structure
SELECT 
    'custodians_columns' as info_type,
    column_name,
    data_type,
    is_nullable,
    column_default,
    '' as constraint_info
FROM information_schema.columns 
WHERE table_name = 'custodians'
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- 6. Show all existing tables
SELECT 
    'all_tables' as info_type,
    table_name as column_name,
    table_type as data_type,
    '' as is_nullable,
    '' as column_default,
    table_schema as constraint_info
FROM information_schema.tables 
WHERE table_schema = 'public'
  AND table_name NOT LIKE 'pg_%'
ORDER BY table_name;

-- 7. Show existing functions
SELECT 
    'existing_functions' as info_type,
    routine_name as column_name,
    routine_type as data_type,
    '' as is_nullable,
    '' as column_default,
    routine_schema as constraint_info
FROM information_schema.routines 
WHERE routine_schema = 'public'
  AND routine_name IN ('update_updated_at_column', 'create_custodian', 'authenticate_custodian', 'assign_default_role');

-- 8. Show RLS policies
SELECT 
    'rls_policies' as info_type,
    policyname as column_name,
    tablename as data_type,
    cmd as is_nullable,
    '' as column_default,
    schemaname as constraint_info
FROM pg_policies 
WHERE schemaname = 'public';

-- 9. Check if specific columns exist in subscription_plans
SELECT 
    'column_check' as info_type,
    'plan_code' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'plan_code'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default,
    'plan_code_check' as constraint_info

UNION ALL

SELECT 
    'column_check' as info_type,
    'cash_box_limit' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'cash_box_limit'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default,
    'cash_box_limit_check' as constraint_info

UNION ALL

SELECT 
    'column_check' as info_type,
    'daily_image_limit' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'daily_image_limit'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default,
    'daily_image_limit_check' as constraint_info

UNION ALL

SELECT 
    'column_check' as info_type,
    'features' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'subscription_plans' 
        AND column_name = 'features'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default,
    'features_check' as constraint_info;

-- 10. Check table existence
SELECT 
    'table_existence' as info_type,
    'subscription_plans' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'subscription_plans'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default,
    'table_check' as constraint_info

UNION ALL

SELECT 
    'table_existence' as info_type,
    'user_roles' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'user_roles'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default,
    'table_check' as constraint_info

UNION ALL

SELECT 
    'table_existence' as info_type,
    'custodians' as column_name,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'custodians'
    ) THEN 'EXISTS' ELSE 'MISSING' END as data_type,
    '' as is_nullable,
    '' as column_default,
    'table_check' as constraint_info;
