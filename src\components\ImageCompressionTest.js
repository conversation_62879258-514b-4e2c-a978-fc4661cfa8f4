import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Image,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { colors } from '../constants/colors';
import ImageCompressionService from '../services/imageCompressionService';
import UsageTrackingService from '../services/usageTrackingService';
import StorageService from '../services/storageService';
import { setupStorageBuckets, testStorageConnection } from '../utils/setupStorage';

/**
 * Image Compression Test Component
 * For testing and demonstrating the compression infrastructure
 * 
 * Features:
 * - Test image selection from camera/gallery
 * - Show compression results
 * - Display usage limits
 * - Test upload workflow
 */

const ImageCompressionTest = ({ userId = 'test-user' }) => {
  const [loading, setLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [compressionResult, setCompressionResult] = useState(null);
  const [usageInfo, setUsageInfo] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState('');

  // Load usage info on component mount
  React.useEffect(() => {
    loadUsageInfo();
  }, []);

  const loadUsageInfo = async () => {
    try {
      const usage = await UsageTrackingService.checkImageUploadLimit(userId);
      setUsageInfo(usage);
    } catch (error) {
      console.error('Error loading usage info:', error);
    }
  };

  const handleImageSelection = async (source) => {
    try {
      setLoading(true);
      setSelectedImage(null);
      setCompressionResult(null);

      console.log(`📱 Selecting image from ${source}...`);

      const result = await ImageCompressionService.selectAndCompressImage(source);
      
      if (result) {
        setSelectedImage(result);
        setCompressionResult(result);
        
        // Refresh usage info
        await loadUsageInfo();
        
        Alert.alert(
          '✅ Compression Complete!',
          `Original: ${ImageCompressionService.formatFileSize(result.originalSize)}\n` +
          `Compressed: ${ImageCompressionService.formatFileSize(result.compressedSize)}\n` +
          `Saved: ${result.compressionRatio}%`,
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleUploadTest = async () => {
    if (!selectedImage) {
      Alert.alert('No Image', 'Please select an image first');
      return;
    }

    try {
      setLoading(true);
      setUploadProgress(0);
      setUploadStatus('Starting upload...');

      const result = await StorageService.uploadTransactionImageWithProgress(
        selectedImage.uri,
        userId,
        (progress, status) => {
          setUploadProgress(progress);
          setUploadStatus(status);
        }
      );

      Alert.alert(
        '🎉 Upload Complete!',
        `File uploaded successfully!\n` +
        `Size: ${ImageCompressionService.formatFileSize(result.compressedSize)}\n` +
        `Compression: ${result.compressionRatio}%\n` +
        `Remaining today: ${result.usage.remaining}`,
        [{ text: 'OK' }]
      );

      // Refresh usage info
      await loadUsageInfo();

    } catch (error) {
      console.error('Error uploading:', error);
      Alert.alert('Upload Error', error.message);
    } finally {
      setLoading(false);
      setUploadProgress(0);
      setUploadStatus('');
    }
  };

  const handleClearUsage = async () => {
    try {
      await UsageTrackingService.clearUsageData();
      await loadUsageInfo();
      Alert.alert('✅ Success', 'Usage data cleared for testing');
    } catch (error) {
      Alert.alert('Error', 'Failed to clear usage data');
    }
  };

  const handleSetupStorage = async () => {
    try {
      setLoading(true);
      setUploadStatus('Setting up storage buckets...');

      const success = await setupStorageBuckets();
      if (success) {
        Alert.alert('✅ Success', 'Storage buckets setup complete!');
      } else {
        Alert.alert('⚠️ Warning', 'Storage setup completed with some issues. Check console for details.');
      }
    } catch (error) {
      Alert.alert('❌ Error', `Storage setup failed: ${error.message}`);
    } finally {
      setLoading(false);
      setUploadStatus('');
    }
  };

  const handleTestConnection = async () => {
    try {
      setLoading(true);
      setUploadStatus('Testing storage connection...');

      const success = await testStorageConnection();
      if (success) {
        Alert.alert('✅ Success', 'Storage connection test passed!');
      } else {
        Alert.alert('❌ Failed', 'Storage connection test failed. Check console for details.');
      }
    } catch (error) {
      Alert.alert('❌ Error', `Connection test failed: ${error.message}`);
    } finally {
      setLoading(false);
      setUploadStatus('');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🧪 Image Compression Test</Text>
      
      {/* Usage Information */}
      {usageInfo && (
        <View style={styles.usageCard}>
          <Text style={styles.cardTitle}>📊 Daily Usage</Text>
          <Text style={styles.usageText}>
            Plan: {usageInfo.planName.toUpperCase()}
          </Text>
          <Text style={styles.usageText}>
            Images: {usageInfo.currentCount}/{usageInfo.limit}
          </Text>
          <Text style={styles.usageText}>
            Remaining: {usageInfo.remaining}
          </Text>
          <Text style={styles.usageText}>
            Resets in: {UsageTrackingService.getTimeUntilReset()}
          </Text>
          
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClearUsage}
            >
              <Text style={styles.clearButtonText}>🧹 Clear Usage</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.setupButton}
              onPress={handleSetupStorage}
              disabled={loading}
            >
              <Text style={styles.setupButtonText}>🔧 Setup Storage</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.testButton}
              onPress={handleTestConnection}
              disabled={loading}
            >
              <Text style={styles.testButtonText}>🧪 Test Connection</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Image Selection Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={() => handleImageSelection('camera')}
          disabled={loading}
        >
          <Text style={styles.buttonText}>📷 Take Photo</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, loading && styles.buttonDisabled]}
          onPress={() => handleImageSelection('gallery')}
          disabled={loading}
        >
          <Text style={styles.buttonText}>🖼️ Pick from Gallery</Text>
        </TouchableOpacity>
      </View>

      {/* Loading Indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>
            {uploadStatus || 'Processing...'}
          </Text>
          {uploadProgress > 0 && (
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { width: `${uploadProgress}%` }]} />
            </View>
          )}
        </View>
      )}

      {/* Selected Image Display */}
      {selectedImage && (
        <View style={styles.imageContainer}>
          <Text style={styles.cardTitle}>📸 Selected Image</Text>
          <Image source={{ uri: selectedImage.uri }} style={styles.previewImage} />
          
          {compressionResult && (
            <View style={styles.compressionInfo}>
              <Text style={styles.infoText}>
                📊 Original: {ImageCompressionService.formatFileSize(compressionResult.originalSize)}
              </Text>
              <Text style={styles.infoText}>
                📦 Compressed: {ImageCompressionService.formatFileSize(compressionResult.compressedSize)}
              </Text>
              <Text style={styles.infoText}>
                📈 Saved: {compressionResult.compressionRatio}%
              </Text>
              <Text style={styles.infoText}>
                🕒 Source: {compressionResult.source}
              </Text>
            </View>
          )}

          <TouchableOpacity
            style={[styles.uploadButton, loading && styles.buttonDisabled]}
            onPress={handleUploadTest}
            disabled={loading || !usageInfo?.canUpload}
          >
            <Text style={styles.uploadButtonText}>
              {usageInfo?.canUpload ? '☁️ Test Upload' : '🚫 Limit Reached'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Instructions */}
      <View style={styles.instructionsCard}>
        <Text style={styles.cardTitle}>📋 Test Instructions</Text>
        <Text style={styles.instructionText}>
          1. Check your daily usage limits above
        </Text>
        <Text style={styles.instructionText}>
          2. Take a photo or pick from gallery
        </Text>
        <Text style={styles.instructionText}>
          3. Review compression results
        </Text>
        <Text style={styles.instructionText}>
          4. Test upload to Supabase (if within limits)
        </Text>
        <Text style={styles.instructionText}>
          5. Use "Clear Usage" to reset for testing
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface,
    textAlign: 'center',
    marginBottom: 20,
  },
  usageCard: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.outline,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.onSurface,
    marginBottom: 12,
  },
  usageText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    flexWrap: 'wrap',
  },
  clearButton: {
    backgroundColor: colors.secondary,
    padding: 8,
    borderRadius: 4,
    flex: 0.3,
    marginRight: 4,
  },
  clearButtonText: {
    color: colors.background,
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
  },
  setupButton: {
    backgroundColor: colors.primary,
    padding: 8,
    borderRadius: 4,
    flex: 0.3,
    marginHorizontal: 2,
  },
  setupButtonText: {
    color: colors.background,
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
  },
  testButton: {
    backgroundColor: '#4CAF50',
    padding: 8,
    borderRadius: 4,
    flex: 0.3,
    marginLeft: 4,
  },
  testButtonText: {
    color: colors.background,
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  button: {
    backgroundColor: colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    flex: 0.48,
  },
  buttonDisabled: {
    backgroundColor: colors.placeholder,
  },
  buttonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  loadingText: {
    color: colors.text,
    marginTop: 8,
    fontSize: 14,
  },
  progressContainer: {
    width: '100%',
    height: 4,
    backgroundColor: colors.outline,
    borderRadius: 2,
    marginTop: 8,
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  imageContainer: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: colors.outline,
  },
  previewImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 12,
  },
  compressionInfo: {
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
  },
  uploadButton: {
    backgroundColor: colors.secondary,
    paddingVertical: 12,
    borderRadius: 8,
  },
  uploadButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  instructionsCard: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: colors.outline,
  },
  instructionText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 6,
  },
});

export default ImageCompressionTest;
