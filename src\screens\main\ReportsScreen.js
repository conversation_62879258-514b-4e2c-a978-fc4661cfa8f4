import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';

const { width } = Dimensions.get('window');

export default function ReportsScreen({ navigation }) {
  const { t } = useTranslation();
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Mock data for now - will be replaced with actual data fetching
  const mockReportData = {
    week: {
      totalExpenses: 1250.75,
      transactionCount: 8,
      topCategories: [
        { name: 'Office Supplies', amount: 450.00, percentage: 36 },
        { name: 'Transportation', amount: 320.50, percentage: 26 },
        { name: 'Meals', amount: 280.25, percentage: 22 },
        { name: 'Utilities', amount: 200.00, percentage: 16 },
      ],
      dailyBreakdown: [
        { day: 'Mon', amount: 150.00 },
        { day: 'Tue', amount: 200.25 },
        { day: 'Wed', amount: 175.50 },
        { day: 'Thu', amount: 300.00 },
        { day: 'Fri', amount: 225.00 },
        { day: 'Sat', amount: 100.00 },
        { day: 'Sun', amount: 100.00 },
      ],
    },
  };

  useEffect(() => {
    loadReportData();
  }, [selectedPeriod]);

  const loadReportData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate loading
      setReportData(mockReportData[selectedPeriod]);
    } catch (error) {
      console.error('Error loading report data:', error);
      Alert.alert('Error', 'Failed to load report data');
    } finally {
      setLoading(false);
    }
  };

  const formatAmount = (amount) => {
    return `₹${amount.toFixed(2)}`;
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      <TouchableOpacity
        style={[styles.periodButton, selectedPeriod === 'week' && styles.activePeriodButton]}
        onPress={() => setSelectedPeriod('week')}
      >
        <Text style={[styles.periodButtonText, selectedPeriod === 'week' && styles.activePeriodButtonText]}>
          Week
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.periodButton, styles.disabledButton]}
        onPress={() => Alert.alert('Premium Feature', 'Monthly reports are available in the premium version')}
      >
        <Text style={[styles.periodButtonText, styles.disabledButtonText]}>
          Month 🔒
        </Text>
      </TouchableOpacity>
      
      <TouchableOpacity
        style={[styles.periodButton, styles.disabledButton]}
        onPress={() => Alert.alert('Premium Feature', 'Yearly reports are available in the premium version')}
      >
        <Text style={[styles.periodButtonText, styles.disabledButtonText]}>
          Year 🔒
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderSummaryCard = () => (
    <View style={styles.summaryCard}>
      <View style={styles.summaryRow}>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Total Expenses</Text>
          <Text style={styles.summaryValue}>{formatAmount(reportData?.totalExpenses || 0)}</Text>
        </View>
        <View style={styles.summaryItem}>
          <Text style={styles.summaryLabel}>Transactions</Text>
          <Text style={styles.summaryValue}>{reportData?.transactionCount || 0}</Text>
        </View>
      </View>
    </View>
  );

  const renderCategoryBreakdown = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Expense Categories</Text>
      {reportData?.topCategories?.map((category, index) => (
        <View key={index} style={styles.categoryItem}>
          <View style={styles.categoryHeader}>
            <Text style={styles.categoryName}>{category.name}</Text>
            <Text style={styles.categoryAmount}>{formatAmount(category.amount)}</Text>
          </View>
          <View style={styles.progressBar}>
            <View 
              style={[styles.progressFill, { width: `${category.percentage}%` }]} 
            />
          </View>
          <Text style={styles.categoryPercentage}>{category.percentage}%</Text>
        </View>
      ))}
    </View>
  );

  const renderDailyBreakdown = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Daily Breakdown</Text>
      <View style={styles.chartContainer}>
        {reportData?.dailyBreakdown?.map((day, index) => {
          const maxAmount = Math.max(...reportData.dailyBreakdown.map(d => d.amount));
          const height = (day.amount / maxAmount) * 100;
          
          return (
            <View key={index} style={styles.chartBar}>
              <View style={styles.barContainer}>
                <View style={[styles.bar, { height: `${height}%` }]} />
              </View>
              <Text style={styles.dayLabel}>{day.day}</Text>
              <Text style={styles.dayAmount}>₹{day.amount.toFixed(0)}</Text>
            </View>
          );
        })}
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={[globalStyles.container, styles.loadingContainer]}>
        <Text>Loading report...</Text>
      </View>
    );
  }

  return (
    <View style={globalStyles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Reports</Text>
        <TouchableOpacity
          onPress={() => Alert.alert('Export', 'Export functionality coming soon!')}
        >
          <Icon name="share" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderPeriodSelector()}
        {renderSummaryCard()}
        {renderCategoryBreakdown()}
        {renderDailyBreakdown()}
        
        <View style={styles.premiumNotice}>
          <Icon name="star" size={20} color={colors.accent} />
          <Text style={styles.premiumText}>
            Upgrade to Premium for advanced reports, monthly/yearly views, and export options
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
    paddingTop: 60, // Account for status bar
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  periodSelector: {
    flexDirection: 'row',
    marginBottom: 20,
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 6,
  },
  activePeriodButton: {
    backgroundColor: colors.primary,
  },
  disabledButton: {
    opacity: 0.6,
  },
  periodButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  activePeriodButtonText: {
    color: colors.background,
  },
  disabledButtonText: {
    color: colors.placeholder,
  },
  summaryCard: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: colors.placeholder,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
  },
  section: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 16,
  },
  categoryItem: {
    marginBottom: 16,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 14,
    color: colors.text,
    fontWeight: '500',
  },
  categoryAmount: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: 'bold',
  },
  progressBar: {
    height: 6,
    backgroundColor: colors.surface,
    borderRadius: 3,
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
  categoryPercentage: {
    fontSize: 12,
    color: colors.placeholder,
    textAlign: 'right',
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 120,
    paddingTop: 20,
  },
  chartBar: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  barContainer: {
    height: 80,
    width: '80%',
    justifyContent: 'flex-end',
  },
  bar: {
    backgroundColor: colors.primary,
    borderRadius: 2,
    minHeight: 4,
  },
  dayLabel: {
    fontSize: 12,
    color: colors.text,
    marginTop: 4,
    fontWeight: '500',
  },
  dayAmount: {
    fontSize: 10,
    color: colors.placeholder,
    marginTop: 2,
  },
  premiumNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 16,
    marginBottom: 20,
  },
  premiumText: {
    flex: 1,
    fontSize: 14,
    color: colors.onSurface,
    marginLeft: 8,
    lineHeight: 20,
  },
});
