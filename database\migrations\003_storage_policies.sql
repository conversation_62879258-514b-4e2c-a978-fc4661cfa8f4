-- Storage Policies for Public Access
-- This migration creates the necessary storage policies for public image access

-- 1. Create storage policies for transaction-images bucket
-- Allow public read access to transaction images
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command, roles)
VALUES (
  'transaction-images-public-read',
  'transaction-images',
  'Public read access for transaction images',
  'true',
  NULL,
  'SELECT',
  '{authenticated, anon}'
) ON CONFLICT (id) DO NOTHING;

-- Allow authenticated users to upload transaction images
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command, roles)
VALUES (
  'transaction-images-authenticated-upload',
  'transaction-images', 
  'Authenticated users can upload transaction images',
  'auth.role() = ''authenticated''',
  'auth.role() = ''authenticated''',
  'INSERT',
  '{authenticated}'
) ON CONFLICT (id) DO NOTHING;

-- Allow users to update their own transaction images
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command, roles)
VALUES (
  'transaction-images-owner-update',
  'transaction-images',
  'Users can update their own transaction images', 
  'auth.uid()::text = (storage.foldername(name))[1]',
  'auth.uid()::text = (storage.foldername(name))[1]',
  'UPDATE',
  '{authenticated}'
) ON CONFLICT (id) DO NOTHING;

-- Allow users to delete their own transaction images
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command, roles)
VALUES (
  'transaction-images-owner-delete',
  'transaction-images',
  'Users can delete their own transaction images',
  'auth.uid()::text = (storage.foldername(name))[1]',
  NULL,
  'DELETE', 
  '{authenticated}'
) ON CONFLICT (id) DO NOTHING;

-- 2. Create storage policies for profile-pictures bucket (if needed)
INSERT INTO storage.policies (id, bucket_id, name, definition, check_definition, command, roles)
VALUES (
  'profile-pictures-public-read',
  'profile-pictures',
  'Public read access for profile pictures',
  'true',
  NULL,
  'SELECT',
  '{authenticated, anon}'
) ON CONFLICT (id) DO NOTHING;

-- 3. Ensure buckets exist and are properly configured
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('transaction-images', 'transaction-images', true, 1048576, '{"image/jpeg","image/jpg"}'),
  ('profile-pictures', 'profile-pictures', true, 524288, '{"image/jpeg","image/jpg","image/png"}'),
  ('voice-memos', 'voice-memos', false, 524288, '{"audio/mp4","audio/mpeg","audio/wav"}')
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 4. Grant necessary permissions
GRANT ALL ON storage.buckets TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT SELECT ON storage.buckets TO anon;
GRANT SELECT ON storage.objects TO anon;

-- 5. Verify setup
DO $$
BEGIN
  RAISE NOTICE 'Storage policies created successfully!';
  RAISE NOTICE 'Buckets configured for public access with proper RLS policies.';
END $$;
