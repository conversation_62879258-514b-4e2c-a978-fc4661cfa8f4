{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@supabase/supabase-js": "^2.51.0", "expo": "^53.0.19", "expo-font": "~13.3.2", "expo-splash-screen": "^0.30.10", "i18next": "^23.5.1", "react": "19.0.0", "react-i18next": "^13.2.2", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.0.0", "expo-status-bar": "~2.2.3", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-media-library": "~17.1.7", "expo-file-system": "~18.1.11"}, "devDependencies": {"@babel/core": "^7.20.0"}}