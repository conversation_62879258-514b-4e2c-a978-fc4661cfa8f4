{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "1.18.2", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@supabase/supabase-js": "^2.51.0", "expo": "^53.0.19", "expo-font": "~11.4.0", "expo-splash-screen": "^0.30.10", "i18next": "^23.5.1", "react": "18.2.0", "react-i18next": "^13.2.2", "react-native": "^0.72.17", "react-native-gesture-handler": "~2.12.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.20.0"}}