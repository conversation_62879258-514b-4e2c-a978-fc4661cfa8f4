import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';
import { createCustodian } from '../../services/custodianService';

export default function AddCustodianScreen({ navigation }) {
  const [formData, setFormData] = useState({
    fullName: '',
    phoneNumber: '',
    pin: '',
    confirmPin: '',
    language: 'en' // Default to English
  });
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showPin, setShowPin] = useState(false);
  const [showConfirmPin, setShowConfirmPin] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate full name
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Name must be at least 2 characters';
    }

    // Validate phone number
    const cleanPhone = formData.phoneNumber.replace(/[\s\-\(\)]/g, '');
    if (!cleanPhone) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^[0-9]{10}$/.test(cleanPhone)) {
      newErrors.phoneNumber = 'Phone number must be exactly 10 digits';
    }

    // Validate PIN
    if (!formData.pin) {
      newErrors.pin = 'PIN is required';
    } else if (!/^[0-9]{6}$/.test(formData.pin)) {
      newErrors.pin = 'PIN must be exactly 6 digits';
    }

    // Validate confirm PIN
    if (!formData.confirmPin) {
      newErrors.confirmPin = 'Please confirm your PIN';
    } else if (formData.pin !== formData.confirmPin) {
      newErrors.confirmPin = 'PINs do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const result = await createCustodian(
        formData.fullName.trim(),
        formData.phoneNumber,
        formData.pin
      );

      if (result.success) {
        Alert.alert(
          'Success',
          'Custodian created successfully! They can now login using their phone number and PIN.',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('Error creating custodian:', error);
      Alert.alert('Error', 'Failed to create custodian. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatPhoneNumber = (text) => {
    // Remove all non-digits
    const cleaned = text.replace(/\D/g, '');
    
    // Limit to 10 digits
    const limited = cleaned.substring(0, 10);
    
    // Format as: 98765 43210
    if (limited.length > 5) {
      return `${limited.substring(0, 5)} ${limited.substring(5)}`;
    }
    
    return limited;
  };

  const handlePhoneChange = (text) => {
    const formatted = formatPhoneNumber(text);
    handleInputChange('phoneNumber', formatted);
  };

  return (
    <View style={styles.container}>
      <AppHeader />
      
      <KeyboardAvoidingView
        style={globalStyles.screenContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation.goBack()}
            >
              <Icon name="arrow-back" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={styles.title}>Add Custodian</Text>
            <View style={styles.placeholder} />
          </View>

          {/* Form Container */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Create New Custodian</Text>
            <Text style={styles.formSubtitle}>
              Add a custodian who can record transactions on your behalf
            </Text>

            {/* Full Name Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Full Name</Text>
              <View style={[styles.inputWrapper, errors.fullName && styles.inputError]}>
                <Icon name="person" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.textInput}
                  placeholder="Enter custodian's full name"
                  placeholderTextColor={colors.placeholder}
                  value={formData.fullName}
                  onChangeText={(text) => handleInputChange('fullName', text)}
                  autoCapitalize="words"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>
              {errors.fullName && <Text style={styles.errorText}>{errors.fullName}</Text>}
            </View>

            {/* Phone Number Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Phone Number</Text>
              <View style={[styles.inputWrapper, errors.phoneNumber && styles.inputError]}>
                <Icon name="phone" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <Text style={styles.countryCode}>+91</Text>
                <TextInput
                  style={styles.textInput}
                  placeholder="98765 43210"
                  placeholderTextColor={colors.placeholder}
                  value={formData.phoneNumber}
                  onChangeText={handlePhoneChange}
                  keyboardType="phone-pad"
                  maxLength={11} // 10 digits + 1 space
                  editable={!loading}
                />
              </View>
              {errors.phoneNumber && <Text style={styles.errorText}>{errors.phoneNumber}</Text>}
              <Text style={styles.helpText}>10-digit Indian mobile number</Text>
            </View>

            {/* PIN Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>6-Digit PIN</Text>
              <View style={[styles.inputWrapper, errors.pin && styles.inputError]}>
                <Icon name="lock" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.textInput}
                  placeholder="Enter 6-digit PIN"
                  placeholderTextColor={colors.placeholder}
                  value={formData.pin}
                  onChangeText={(text) => handleInputChange('pin', text.replace(/\D/g, '').substring(0, 6))}
                  secureTextEntry={!showPin}
                  keyboardType="numeric"
                  maxLength={6}
                  editable={!loading}
                />
                <TouchableOpacity
                  onPress={() => setShowPin(!showPin)}
                  style={styles.eyeIcon}
                >
                  <Icon
                    name={showPin ? 'visibility' : 'visibility-off'}
                    size={18}
                    color={colors.placeholder}
                  />
                </TouchableOpacity>
              </View>
              {errors.pin && <Text style={styles.errorText}>{errors.pin}</Text>}
              <Text style={styles.helpText}>Custodian will use this PIN to login</Text>
            </View>

            {/* Confirm PIN Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Confirm PIN</Text>
              <View style={[styles.inputWrapper, errors.confirmPin && styles.inputError]}>
                <Icon name="lock" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.textInput}
                  placeholder="Re-enter 6-digit PIN"
                  placeholderTextColor={colors.placeholder}
                  value={formData.confirmPin}
                  onChangeText={(text) => handleInputChange('confirmPin', text.replace(/\D/g, '').substring(0, 6))}
                  secureTextEntry={!showConfirmPin}
                  keyboardType="numeric"
                  maxLength={6}
                  editable={!loading}
                />
                <TouchableOpacity
                  onPress={() => setShowConfirmPin(!showConfirmPin)}
                  style={styles.eyeIcon}
                >
                  <Icon
                    name={showConfirmPin ? 'visibility' : 'visibility-off'}
                    size={18}
                    color={colors.placeholder}
                  />
                </TouchableOpacity>
              </View>
              {errors.confirmPin && <Text style={styles.errorText}>{errors.confirmPin}</Text>}
            </View>

            {/* Language Selection */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Preferred Language</Text>
              <View style={styles.languageContainer}>
                <TouchableOpacity
                  style={[styles.languageOption, formData.language === 'en' && styles.selectedLanguage]}
                  onPress={() => handleInputChange('language', 'en')}
                  disabled={loading}
                >
                  <Text style={[styles.languageText, formData.language === 'en' && styles.selectedLanguageText]}>
                    🇺🇸 English
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.languageOption, formData.language === 'hi' && styles.selectedLanguage]}
                  onPress={() => handleInputChange('language', 'hi')}
                  disabled={loading}
                >
                  <Text style={[styles.languageText, formData.language === 'hi' && styles.selectedLanguageText]}>
                    🇮🇳 हिंदी
                  </Text>
                </TouchableOpacity>
              </View>
              <Text style={styles.helpText}>Custodian will see the app in this language</Text>
            </View>

            {/* Info Box */}
            <View style={styles.infoBox}>
              <Icon name="info" size={20} color={colors.primary} />
              <Text style={styles.infoText}>
                The custodian will login using their phone number and PIN through the same app.
              </Text>
            </View>

            {/* Create Button */}
            <TouchableOpacity
              style={[styles.createButton, loading && styles.disabledButton]}
              onPress={handleSubmit}
              disabled={loading}
            >
              <Text style={styles.createButtonText}>
                {loading ? 'Creating...' : 'Create Custodian'}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
  },
  placeholder: {
    width: 40, // Same width as back button for centering
  },
  formContainer: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 3,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
    marginBottom: 8,
  },
  formSubtitle: {
    fontSize: 14,
    color: colors.text, // Global text color
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text, // Global text color
    marginBottom: 6,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 4, // Global 4px border radius
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: 12,
    height: 44,
  },
  inputError: {
    borderColor: colors.error,
  },
  inputIcon: {
    marginRight: 8,
  },
  countryCode: {
    fontSize: 16,
    color: colors.text, // Global text color
    marginRight: 8,
    fontWeight: '500',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text, // Global text color
    paddingVertical: 0,
  },
  eyeIcon: {
    padding: 4,
  },
  errorText: {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
    marginLeft: 4,
  },
  helpText: {
    fontSize: 12,
    color: colors.text, // Global text color
    marginTop: 4,
    marginLeft: 4,
    opacity: 0.7,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: colors.primary + '10',
    borderRadius: 4, // Global 4px border radius
    padding: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: colors.primary + '30',
  },
  infoText: {
    fontSize: 14,
    color: colors.text, // Global text color
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
  createButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  createButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  languageContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  languageOption: {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: 4, // Global 4px border radius
    borderWidth: 1,
    borderColor: colors.border,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  selectedLanguage: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  languageText: {
    fontSize: 14,
    color: colors.text, // Global text color
    fontWeight: '500',
  },
  selectedLanguageText: {
    color: colors.primary,
    fontWeight: '600',
  },
});
