import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';
import { TransactionService } from '../../services/transactionService';
import { getCustodians } from '../../services/custodianService';
import { useAuth } from '../../context/AuthContext';

export default function CreateCashBoxScreen({ navigation }) {
  const { t } = useTranslation();
  const { user } = useAuth();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    initialAmount: '',
    assignedTo: 'SELF', // Default to SELF
  });

  // Data state
  const [custodians, setCustodians] = useState([]);
  const [loadingCustodians, setLoadingCustodians] = useState(true);

  // UI state
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showAssignmentPicker, setShowAssignmentPicker] = useState(false);

  useEffect(() => {
    loadCustodians();
  }, []);

  const loadCustodians = async () => {
    try {
      setLoadingCustodians(true);
      const result = await getCustodians();

      if (result.success) {
        setCustodians(result.data || []);
      } else {
        console.error('Error loading custodians:', result.error);
        // Don't show error alert, just log it - SELF option still works
      }
    } catch (error) {
      console.error('Error loading custodians:', error);
    } finally {
      setLoadingCustodians(false);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Cash box name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }

    if (formData.initialAmount && isNaN(parseFloat(formData.initialAmount))) {
      newErrors.initialAmount = 'Please enter a valid amount';
    }

    if (!formData.assignedTo) {
      newErrors.assignedTo = 'Please select who will record transactions';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const getAssignmentDisplayName = () => {
    if (formData.assignedTo === 'SELF') {
      return 'SELF (You will record transactions)';
    }

    const custodian = custodians.find(c => c.id === formData.assignedTo);
    return custodian ? `${custodian.full_name} (${custodian.phone_number})` : 'Select Assignment';
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Create the cash box first
      const result = await TransactionService.createCashBox(
        formData.name.trim(),
        formData.description.trim() || null,
        parseFloat(formData.initialAmount) || 0
      );

      if (result.success) {
        // TODO: In next step, we'll add assignment logic to database
        // For now, just show success with assignment info
        const assignmentText = formData.assignedTo === 'SELF'
          ? 'You can now record transactions yourself.'
          : `${getAssignmentDisplayName().split(' (')[0]} can now record transactions.`;

        Alert.alert(
          'Success',
          `Cash box created successfully! ${assignmentText}`,
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('Error creating cash box:', error);
      Alert.alert('Error', 'Failed to create cash box. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <AppHeader />

      <KeyboardAvoidingView
        style={globalStyles.screenContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Create Cash Box</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Form Container */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>New Cash Box</Text>
            <Text style={styles.formSubtitle}>
              Create a cash box to organize and track your petty cash transactions
            </Text>

            {/* Cash Box Name */}
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Cash Box Name *</Text>
              <TextInput
                style={[styles.textInput, errors.name && styles.errorInput]}
                value={formData.name}
                onChangeText={(text) => {
                  setFormData(prev => ({ ...prev, name: text }));
                  if (errors.name) {
                    setErrors(prev => ({ ...prev, name: null }));
                  }
                }}
                placeholder="e.g., Main Office, Site A, Event Fund"
                placeholderTextColor={colors.placeholder}
                returnKeyType="next"
                maxLength={50}
              />
              {errors.name && (
                <Text style={styles.errorText}>{errors.name}</Text>
              )}
            </View>

            {/* Description */}
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Description (Optional)</Text>
              <TextInput
                style={styles.textAreaInput}
                value={formData.description}
                onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                placeholder="Add details about this cash box..."
                placeholderTextColor={colors.placeholder}
                multiline
                numberOfLines={3}
                textAlignVertical="top"
                maxLength={200}
              />
            </View>

            {/* Initial Amount */}
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Initial Amount (Optional)</Text>
              <View style={styles.amountContainer}>
                <Text style={styles.currencySymbol}>₹</Text>
                <TextInput
                  style={[styles.amountInput, errors.initialAmount && styles.errorInput]}
                  value={formData.initialAmount}
                  onChangeText={(text) => {
                    // Only allow numbers and decimal point
                    const cleanText = text.replace(/[^0-9.]/g, '');
                    // Prevent multiple decimal points
                    const parts = cleanText.split('.');
                    if (parts.length > 2) {
                      return;
                    }
                    setFormData(prev => ({ ...prev, initialAmount: cleanText }));
                    if (errors.initialAmount) {
                      setErrors(prev => ({ ...prev, initialAmount: null }));
                    }
                  }}
                  placeholder="0.00"
                  placeholderTextColor={colors.placeholder}
                  keyboardType="decimal-pad"
                  returnKeyType="done"
                />
              </View>
              {errors.initialAmount && (
                <Text style={styles.errorText}>{errors.initialAmount}</Text>
              )}
              <Text style={styles.helpText}>
                The starting amount in this cash box
              </Text>
            </View>

            {/* Assignment Selection */}
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Who will record transactions? *</Text>
              <TouchableOpacity
                style={[styles.pickerButton, errors.assignedTo && styles.errorInput]}
                onPress={() => setShowAssignmentPicker(!showAssignmentPicker)}
              >
                <Text style={[styles.pickerText, !formData.assignedTo && styles.placeholderText]}>
                  {getAssignmentDisplayName()}
                </Text>
                <Icon name="arrow-drop-down" size={24} color={colors.text} />
              </TouchableOpacity>

              {showAssignmentPicker && (
                <View style={styles.pickerContainer}>
                  {/* SELF Option */}
                  <TouchableOpacity
                    style={styles.pickerItem}
                    onPress={() => {
                      setFormData(prev => ({ ...prev, assignedTo: 'SELF' }));
                      setShowAssignmentPicker(false);
                      if (errors.assignedTo) {
                        setErrors(prev => ({ ...prev, assignedTo: null }));
                      }
                    }}
                  >
                    <View style={styles.assignmentOption}>
                      <Icon name="person" size={20} color={colors.primary} />
                      <View style={styles.assignmentText}>
                        <Text style={styles.pickerItemText}>SELF</Text>
                        <Text style={styles.pickerItemSubtext}>You will record transactions</Text>
                      </View>
                    </View>
                  </TouchableOpacity>

                  {/* Custodians */}
                  {custodians.map((custodian) => (
                    <TouchableOpacity
                      key={custodian.id}
                      style={styles.pickerItem}
                      onPress={() => {
                        setFormData(prev => ({ ...prev, assignedTo: custodian.id }));
                        setShowAssignmentPicker(false);
                        if (errors.assignedTo) {
                          setErrors(prev => ({ ...prev, assignedTo: null }));
                        }
                      }}
                    >
                      <View style={styles.assignmentOption}>
                        <Icon name="person-outline" size={20} color={colors.text} />
                        <View style={styles.assignmentText}>
                          <Text style={styles.pickerItemText}>{custodian.full_name}</Text>
                          <Text style={styles.pickerItemSubtext}>{custodian.phone_number}</Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  ))}

                  {custodians.length === 0 && !loadingCustodians && (
                    <View style={styles.pickerItem}>
                      <Text style={styles.noCustodiansText}>
                        No custodians created yet. Create custodians first or use SELF option.
                      </Text>
                    </View>
                  )}
                </View>
              )}

              {errors.assignedTo && (
                <Text style={styles.errorText}>{errors.assignedTo}</Text>
              )}
              <Text style={styles.helpText}>
                Select who will be responsible for recording transactions in this cash box
              </Text>
            </View>

            {/* Info Box */}
            <View style={styles.infoBox}>
              <Icon name="info" size={20} color={colors.primary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>Free Plan Limit</Text>
                <Text style={styles.infoText}>
                  You can create 1 cash box on the free plan. Upgrade to create multiple cash boxes.
                </Text>
              </View>
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              style={[globalStyles.primaryButton, loading && styles.disabledButton]}
              onPress={handleSubmit}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.background} />
              ) : (
                <Text style={globalStyles.buttonText}>Create Cash Box</Text>
              )}
            </TouchableOpacity>

          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface,
  },
  placeholder: {
    width: 40,
  },
  scrollContainer: {
    paddingBottom: 27, // Global 27px margin bottom
  },
  formContainer: {
    padding: 16,
  },
  formTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: 8,
  },
  formSubtitle: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 32,
    lineHeight: 22,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.onSurface,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    padding: 16,
    fontSize: 16,
    color: colors.text,
    minHeight: 56,
  },
  textAreaInput: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    padding: 16,
    fontSize: 16,
    color: colors.text,
    minHeight: 80,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    paddingHorizontal: 16,
    minHeight: 56,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    color: colors.text,
    padding: 0,
  },
  errorInput: {
    borderColor: colors.error,
  },
  errorText: {
    fontSize: 14,
    color: colors.error,
    marginTop: 4,
  },
  helpText: {
    fontSize: 14,
    color: colors.placeholder,
    marginTop: 4,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: colors.primaryLight,
    borderRadius: 4,
    padding: 16,
    marginBottom: 24,
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
    marginBottom: 4,
  },
  infoText: {
    fontSize: 14,
    color: colors.text,
    lineHeight: 20,
  },
  disabledButton: {
    opacity: 0.5,
  },
  pickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    padding: 16,
    minHeight: 56,
  },
  pickerText: {
    fontSize: 16,
    color: colors.text,
    flex: 1,
  },
  placeholderText: {
    color: colors.placeholder,
  },
  pickerContainer: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    marginTop: 4,
    maxHeight: 300,
  },
  pickerItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  pickerItemText: {
    fontSize: 16,
    color: colors.text,
    fontWeight: '600',
  },
  pickerItemSubtext: {
    fontSize: 14,
    color: colors.placeholder,
    marginTop: 2,
  },
  assignmentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  assignmentText: {
    flex: 1,
  },
  noCustodiansText: {
    fontSize: 14,
    color: colors.placeholder,
    fontStyle: 'italic',
    textAlign: 'center',
  },
});