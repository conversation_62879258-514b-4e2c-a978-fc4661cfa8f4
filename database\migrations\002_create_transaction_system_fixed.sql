-- =====================================================
-- KharchaPani Transaction System (FIXED VERSION)
-- =====================================================
-- Creates tables for cash boxes, expense categories, and transactions
-- Safe to run multiple times (uses IF NOT EXISTS)

-- 1. Create or update subscription_plans table
DO $$
BEGIN
    -- Check if subscription_plans table exists
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'subscription_plans') THEN
        -- Create the table if it doesn't exist
        CREATE TABLE subscription_plans (
            id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
            plan_name text NOT NULL UNIQUE,
            custodian_limit integer NOT NULL DEFAULT 1,
            cash_box_limit integer NOT NULL DEFAULT 1,
            daily_image_limit integer NOT NULL DEFAULT 3,
            features jsonb DEFAULT '{}',
            price_monthly decimal(10,2) DEFAULT 0,
            created_at timestamp with time zone DEFAULT now(),
            updated_at timestamp with time zone DEFAULT now()
        );
    ELSE
        -- Add missing columns if table exists
        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'cash_box_limit') THEN
            ALTER TABLE subscription_plans ADD COLUMN cash_box_limit integer NOT NULL DEFAULT 1;
        END IF;
        
        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'daily_image_limit') THEN
            ALTER TABLE subscription_plans ADD COLUMN daily_image_limit integer NOT NULL DEFAULT 3;
        END IF;
        
        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'features') THEN
            ALTER TABLE subscription_plans ADD COLUMN features jsonb DEFAULT '{}';
        END IF;
        
        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'price_monthly') THEN
            ALTER TABLE subscription_plans ADD COLUMN price_monthly decimal(10,2) DEFAULT 0;
        END IF;
    END IF;
END $$;

-- 2. Insert default plans (with conflict handling)
INSERT INTO subscription_plans (plan_name, custodian_limit, cash_box_limit, daily_image_limit, features, price_monthly)
VALUES 
  ('free', 1, 1, 3, '{"voice_memos": false, "advanced_reports": false, "description_field": false, "image_upload": true, "transaction_management": true}', 0),
  ('plan_1', 5, 3, 10, '{"voice_memos": true, "advanced_reports": true, "description_field": true, "image_upload": true, "transaction_management": true}', 299),
  ('plan_2', 10, 5, 20, '{"voice_memos": true, "advanced_reports": true, "description_field": true, "image_upload": true, "transaction_management": true}', 499),
  ('enterprise', 999, 999, 100, '{"voice_memos": true, "advanced_reports": true, "description_field": true, "image_upload": true, "transaction_management": true}', 999)
ON CONFLICT (plan_name) DO UPDATE SET
  custodian_limit = EXCLUDED.custodian_limit,
  cash_box_limit = EXCLUDED.cash_box_limit,
  daily_image_limit = EXCLUDED.daily_image_limit,
  features = EXCLUDED.features,
  price_monthly = EXCLUDED.price_monthly;

-- 3. Update user_roles table to include subscription plan (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'user_roles' AND column_name = 'subscription_plan_id') THEN
        ALTER TABLE user_roles ADD COLUMN subscription_plan_id uuid REFERENCES subscription_plans(id) DEFAULT (SELECT id FROM subscription_plans WHERE plan_name = 'free');
    END IF;
    
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'user_roles' AND column_name = 'preferred_language') THEN
        ALTER TABLE user_roles ADD COLUMN preferred_language text DEFAULT 'en';
    END IF;
END $$;

-- 4. Create cash_boxes table
CREATE TABLE IF NOT EXISTS cash_boxes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  issuer_user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  initial_amount decimal(10,2) DEFAULT 0,
  current_balance decimal(10,2) DEFAULT 0,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Ensure unique cash box names per issuer
  UNIQUE(issuer_user_id, name)
);

-- 5. Create expense_categories table
CREATE TABLE IF NOT EXISTS expense_categories (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_box_id uuid REFERENCES cash_boxes(id) ON DELETE CASCADE,
  name text NOT NULL,
  is_predefined boolean DEFAULT false,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  
  -- Ensure unique category names per cash box
  UNIQUE(cash_box_id, name)
);

-- 6. Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_box_id uuid NOT NULL REFERENCES cash_boxes(id) ON DELETE CASCADE,
  custodian_user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  expense_category_id uuid NOT NULL REFERENCES expense_categories(id),
  amount decimal(10,2) NOT NULL CHECK (amount > 0),
  description text,
  
  -- Image and voice memo fields (premium features)
  image_url text,
  image_filename text,
  voice_memo_url text,
  voice_memo_filename text,
  
  -- Metadata
  transaction_date timestamp with time zone DEFAULT now(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Ensure positive amounts
  CONSTRAINT positive_amount CHECK (amount > 0)
);

-- 7. Create custodians table (if not exists from previous migration)
CREATE TABLE IF NOT EXISTS custodians (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  issuer_user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL, -- Actual user account (optional)
  full_name text NOT NULL,
  phone text NOT NULL UNIQUE,
  pin_hash text NOT NULL,
  preferred_language text DEFAULT 'en',
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  
  -- Ensure unique phone numbers
  UNIQUE(phone)
);

-- 8. Create cash_box_custodians junction table (many-to-many)
CREATE TABLE IF NOT EXISTS cash_box_custodians (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  cash_box_id uuid NOT NULL REFERENCES cash_boxes(id) ON DELETE CASCADE,
  custodian_id uuid NOT NULL REFERENCES custodians(id) ON DELETE CASCADE,
  assigned_at timestamp with time zone DEFAULT now(),
  assigned_by uuid REFERENCES auth.users(id),
  
  -- Ensure unique assignments
  UNIQUE(cash_box_id, custodian_id)
);

-- 9. Insert predefined expense categories function
CREATE OR REPLACE FUNCTION create_predefined_categories(cash_box_uuid uuid)
RETURNS void AS $$
BEGIN
  INSERT INTO expense_categories (cash_box_id, name, is_predefined)
  VALUES 
    (cash_box_uuid, 'Office Supplies', true),
    (cash_box_uuid, 'Transportation', true),
    (cash_box_uuid, 'Food & Beverages', true),
    (cash_box_uuid, 'Utilities', true),
    (cash_box_uuid, 'Maintenance', true),
    (cash_box_uuid, 'Communication', true),
    (cash_box_uuid, 'Miscellaneous', true),
    (cash_box_uuid, 'Other', true)
  ON CONFLICT (cash_box_id, name) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Create function to create cash box with categories
CREATE OR REPLACE FUNCTION create_cash_box_with_categories(
  issuer_id uuid,
  box_name text,
  box_description text DEFAULT NULL,
  initial_amt decimal DEFAULT 0
)
RETURNS uuid AS $$
DECLARE
  new_cash_box_id uuid;
  user_plan_limit integer;
  current_count integer;
BEGIN
  -- Check user's plan limits
  SELECT sp.cash_box_limit INTO user_plan_limit
  FROM user_roles ur
  JOIN subscription_plans sp ON ur.subscription_plan_id = sp.id
  WHERE ur.user_id = issuer_id;
  
  -- If no plan found, default to free plan limit
  IF user_plan_limit IS NULL THEN
    user_plan_limit := 1;
  END IF;
  
  -- Count current cash boxes
  SELECT COUNT(*) INTO current_count
  FROM cash_boxes
  WHERE issuer_user_id = issuer_id AND is_active = true;
  
  -- Check if user can create more cash boxes
  IF current_count >= user_plan_limit THEN
    RAISE EXCEPTION 'Cash box limit reached. Current plan allows % cash boxes.', user_plan_limit;
  END IF;
  
  -- Create cash box
  INSERT INTO cash_boxes (issuer_user_id, name, description, initial_amount, current_balance)
  VALUES (issuer_id, box_name, box_description, initial_amt, initial_amt)
  RETURNING id INTO new_cash_box_id;
  
  -- Create predefined categories
  PERFORM create_predefined_categories(new_cash_box_id);
  
  RETURN new_cash_box_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. Create function to record transaction
CREATE OR REPLACE FUNCTION create_transaction(
  p_cash_box_id uuid,
  p_custodian_user_id uuid,
  p_expense_category_id uuid,
  p_amount decimal,
  p_description text DEFAULT NULL,
  p_image_url text DEFAULT NULL,
  p_image_filename text DEFAULT NULL,
  p_voice_memo_url text DEFAULT NULL,
  p_voice_memo_filename text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  new_transaction_id uuid;
  current_balance decimal;
BEGIN
  -- Get current balance
  SELECT current_balance INTO current_balance
  FROM cash_boxes
  WHERE id = p_cash_box_id;

  -- Create transaction
  INSERT INTO transactions (
    cash_box_id, custodian_user_id, expense_category_id, amount, description,
    image_url, image_filename, voice_memo_url, voice_memo_filename
  )
  VALUES (
    p_cash_box_id, p_custodian_user_id, p_expense_category_id, p_amount, p_description,
    p_image_url, p_image_filename, p_voice_memo_url, p_voice_memo_filename
  )
  RETURNING id INTO new_transaction_id;

  -- Update cash box balance
  UPDATE cash_boxes
  SET current_balance = current_balance - p_amount,
      updated_at = now()
  WHERE id = p_cash_box_id;

  RETURN new_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. Create triggers for updated_at (only if function exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
        DROP TRIGGER IF EXISTS update_cash_boxes_updated_at ON cash_boxes;
        CREATE TRIGGER update_cash_boxes_updated_at
          BEFORE UPDATE ON cash_boxes
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

        DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
        CREATE TRIGGER update_transactions_updated_at
          BEFORE UPDATE ON transactions
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

        DROP TRIGGER IF EXISTS update_custodians_updated_at ON custodians;
        CREATE TRIGGER update_custodians_updated_at
          BEFORE UPDATE ON custodians
          FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 13. Enable RLS on all tables
ALTER TABLE cash_boxes ENABLE ROW LEVEL SECURITY;
ALTER TABLE expense_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE custodians ENABLE ROW LEVEL SECURITY;
ALTER TABLE cash_box_custodians ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;

-- 14. Create RLS policies (drop existing first to avoid conflicts)

-- Cash boxes: Users can only see their own cash boxes
DROP POLICY IF EXISTS "users_can_manage_own_cash_boxes" ON cash_boxes;
CREATE POLICY "users_can_manage_own_cash_boxes" ON cash_boxes
  FOR ALL
  USING (auth.uid() = issuer_user_id);

-- Expense categories: Users can see categories for their cash boxes
DROP POLICY IF EXISTS "users_can_manage_expense_categories" ON expense_categories;
CREATE POLICY "users_can_manage_expense_categories" ON expense_categories
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = expense_categories.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
  );

-- Transactions: Users can see transactions for their cash boxes OR transactions they created as custodians
DROP POLICY IF EXISTS "users_can_view_relevant_transactions" ON transactions;
CREATE POLICY "users_can_view_relevant_transactions" ON transactions
  FOR SELECT
  USING (
    -- Issuer can see all transactions in their cash boxes
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = transactions.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
    OR
    -- Custodians can see their own transactions
    auth.uid() = custodian_user_id
  );

DROP POLICY IF EXISTS "custodians_can_create_transactions" ON transactions;
CREATE POLICY "custodians_can_create_transactions" ON transactions
  FOR INSERT
  WITH CHECK (auth.uid() = custodian_user_id);

DROP POLICY IF EXISTS "issuers_can_manage_transactions" ON transactions;
CREATE POLICY "issuers_can_manage_transactions" ON transactions
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = transactions.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
  );

-- Custodians: Issuers can manage their custodians
DROP POLICY IF EXISTS "issuers_can_manage_custodians" ON custodians;
CREATE POLICY "issuers_can_manage_custodians" ON custodians
  FOR ALL
  USING (auth.uid() = issuer_user_id);

-- Cash box custodians: Issuers can manage assignments
DROP POLICY IF EXISTS "issuers_can_manage_assignments" ON cash_box_custodians;
CREATE POLICY "issuers_can_manage_assignments" ON cash_box_custodians
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = cash_box_custodians.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
  );

-- Subscription plans: Everyone can read (for plan selection)
DROP POLICY IF EXISTS "everyone_can_read_plans" ON subscription_plans;
CREATE POLICY "everyone_can_read_plans" ON subscription_plans
  FOR SELECT
  USING (true);

-- 15. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cash_boxes_issuer ON cash_boxes(issuer_user_id);
CREATE INDEX IF NOT EXISTS idx_cash_boxes_active ON cash_boxes(is_active);
CREATE INDEX IF NOT EXISTS idx_expense_categories_cash_box ON expense_categories(cash_box_id);
CREATE INDEX IF NOT EXISTS idx_transactions_cash_box ON transactions(cash_box_id);
CREATE INDEX IF NOT EXISTS idx_transactions_custodian ON transactions(custodian_user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_custodians_issuer ON custodians(issuer_user_id);
CREATE INDEX IF NOT EXISTS idx_custodians_phone ON custodians(phone);
CREATE INDEX IF NOT EXISTS idx_cash_box_custodians_box ON cash_box_custodians(cash_box_id);
CREATE INDEX IF NOT EXISTS idx_cash_box_custodians_custodian ON cash_box_custodians(custodian_id);

-- 16. Create view for transaction details
CREATE OR REPLACE VIEW transaction_details AS
SELECT
  t.id,
  t.cash_box_id,
  t.amount,
  t.description,
  t.image_url,
  t.image_filename,
  t.voice_memo_url,
  t.voice_memo_filename,
  t.transaction_date,
  t.created_at,

  -- Cash box info
  cb.name as cash_box_name,
  cb.issuer_user_id,

  -- Category info
  ec.name as category_name,

  -- Custodian info
  c.full_name as custodian_name,
  c.phone as custodian_phone,
  t.custodian_user_id

FROM transactions t
JOIN cash_boxes cb ON t.cash_box_id = cb.id
JOIN expense_categories ec ON t.expense_category_id = ec.id
LEFT JOIN custodians c ON t.custodian_user_id = c.user_id;

-- Grant permissions
GRANT SELECT ON transaction_details TO authenticated;

-- 17. Update existing users to have free plan (if they don't have one)
UPDATE user_roles
SET subscription_plan_id = (SELECT id FROM subscription_plans WHERE plan_name = 'free')
WHERE subscription_plan_id IS NULL;
