-- =====================================================
-- Fix create_transaction function - Ambiguous column reference
-- =====================================================
-- Fixes the "current_balance" ambiguous column error

-- Drop and recreate the function with proper table aliases
CREATE OR REPLACE FUNCTION create_transaction(
  p_cash_box_id uuid,
  p_custodian_user_id uuid,
  p_expense_category_id uuid,
  p_amount decimal,
  p_description text DEFAULT NULL,
  p_image_url text DEFAULT NULL,
  p_image_filename text DEFAULT NULL,
  p_voice_memo_url text DEFAULT NULL,
  p_voice_memo_filename text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  new_transaction_id uuid;
  current_balance decimal;
BEGIN
  -- Get current balance with explicit table reference
  SELECT cb.current_balance INTO current_balance
  FROM cash_boxes cb
  WHERE cb.id = p_cash_box_id;
  
  -- Create transaction
  INSERT INTO transactions (
    cash_box_id, custodian_user_id, expense_category_id, amount, description,
    image_url, image_filename, voice_memo_url, voice_memo_filename
  )
  VALUES (
    p_cash_box_id, p_custodian_user_id, p_expense_category_id, p_amount, p_description,
    p_image_url, p_image_filename, p_voice_memo_url, p_voice_memo_filename
  )
  RETURNING id INTO new_transaction_id;
  
  -- Update cash box balance with explicit table reference
  UPDATE cash_boxes cb
  SET current_balance = cb.current_balance - p_amount,
      updated_at = now()
  WHERE cb.id = p_cash_box_id;
  
  RETURN new_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
