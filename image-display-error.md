This is the CONSOLE Log:

Auth state changed: SIGNED_IN Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:18 === ROLE CHECK ===
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:19 Current user role: null
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:20 User email: <EMAIL>
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:21 Is superadmin: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:22 Can manage roles: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:23 ==================
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:18 === ROLE CHECK ===
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:19 Current user role: superadmin
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:20 User email: <EMAIL>
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:21 Is superadmin: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:22 Can manage roles: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\HomeScreen.js:23 ==================
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\languageService.js:76 Language changed to: en
D:\currentprojects\LATEST\KharchaPani\khrpani\src\services\languageService.js:219 Applied language on login: en for issuer
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\auth\SmartLoginScreen.js:104 Login successful
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:46 🔄 Screen focused - refreshing transactions
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: null
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: null
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:47 🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:47 🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: null
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: null
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:57 ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:57 ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: false
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:99 ❌ Image load error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:100 ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:99 ❌ Image load error: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:100 ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:89 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:89 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:89 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:89 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:89 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\screens\main\TransactionsScreen.js:133 🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:142 🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:81 🖼️ ImagePreview Debug:
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:82   - imageUri: undefined
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:83   - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:84   - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:85   - imageSource: Object
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:86   - imageError: true
D:\currentprojects\LATEST\KharchaPani\khrpani\src\components\ImagePreview.js:89 ❌ ImagePreview: No image source or error, showing placeholder
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:148 ✅ Image URL accessible
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:149 📊 Response status: 200
D:\currentprojects\LATEST\KharchaPani\khrpani\src\utils\setupStorage.js:150 📋 Content type: image/jpeg
Welcome to React Native DevTools
Debugger integration: Android Bridgeless (ReactHostImpl)


This is the CMD Window log:
 
 LOG  === ROLE CHECK ===
 LOG  Current user role: null
 LOG  User email: <EMAIL>
 LOG  Is superadmin: false
 LOG  Can manage roles: false
 LOG  ==================
 LOG  === ROLE CHECK ===
 LOG  Current user role: superadmin
 LOG  User email: <EMAIL>
 LOG  Is superadmin: true
 LOG  Can manage roles: true
 LOG  ==================
 LOG  Language changed to: en
 LOG  Applied language on login: en for issuer
 LOG  Login successful
 LOG  🔄 Screen focused - refreshing transactions
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG    - signedUrl: null
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg"}
 LOG    - imageError: false
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG    - signedUrl: null
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg"}
 LOG    - imageError: false
 LOG  🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🔐 Generating signed URL for: eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG    - signedUrl: null
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg"}
 LOG    - imageError: false
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG    - signedUrl: null
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg"}
 LOG    - imageError: false
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 LOG  ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
 LOG  ✅ Signed URL generated: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us"}
 LOG    - imageError: false
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU"}
 LOG    - imageError: false
 LOG  ❌ Image load error: {"_dispatchInstances": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.4269990026950836, "actualStartTime": 56779971.870229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03576900064945221, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043230004608631134, "type": [Object], "updateQueue": null}, "actualDuration": 0.386230006814003, "actualStartTime": 56779971.90746, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.15692299604415894, "actualStartTime": 56779876.67146, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.008922994136810303, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.008922994136810303, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.4269990026950836, "actualStartTime": 56779971.870229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03576900064945221, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043230004608631134, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.0074610039591789246, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.0074610039591789246, "type": "RCTImageView", "updateQueue": null}, "_dispatchListeners": [Function handleImageError], "_targetInst": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.4269990026950836, "actualStartTime": 56779971.870229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03576900064945221, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043230004608631134, "type": [Object], "updateQueue": null}, "actualDuration": 0.386230006814003, "actualStartTime": 56779971.90746, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.15692299604415894, "actualStartTime": 56779876.67146, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.008922994136810303, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.008922994136810303, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.4269990026950836, "actualStartTime": 56779971.870229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.03576900064945221, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.043230004608631134, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.0074610039591789246, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.0074610039591789246, "type": "RCTImageView", "updateQueue": null}, "bubbles": undefined, "cancelable": undefined, "currentTarget": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.386230006814003, "actualStartTime": 56779971.90746, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.0074610039591789246, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.0074610039591789246, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 600, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "defaultPrevented": undefined, "dispatchConfig": {"registrationName": "onError"}, "eventPhase": undefined, "isDefaultPrevented": [Function functionThatReturnsFalse], "isPropagationStopped": [Function functionThatReturnsFalse], "isTrusted": undefined, "nativeEvent": {"error": "unknown image format", "target": 600}, "target": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.386230006814003, "actualStartTime": 56779971.90746, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.0074610039591789246, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.0074610039591789246, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 600, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "timeStamp": 1752913388321, "type": undefined}
 LOG  ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  ❌ Image load error: {"_dispatchInstances": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3384620025753975, "actualStartTime": 56779979.587229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.0366160050034523, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.044307999312877655, "type": [Object], "updateQueue": null}, "actualDuration": 0.2969229966402054, "actualStartTime": 56779979.625306, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.13515499979257584, "actualStartTime": 56779894.405921, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.0077700018882751465, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.0077700018882751465, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3384620025753975, "actualStartTime": 56779979.587229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.0366160050034523, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.044307999312877655, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.007691994309425354, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007691994309425354, "type": "RCTImageView", "updateQueue": null}, "_dispatchListeners": [Function handleImageError], "_targetInst": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3384620025753975, "actualStartTime": 56779979.587229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.0366160050034523, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.044307999312877655, "type": [Object], "updateQueue": null}, "actualDuration": 0.2969229966402054, "actualStartTime": 56779979.625306, "alternate": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.13515499979257584, "actualStartTime": 56779894.405921, "alternate": [Circular], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097152, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.0077700018882751465, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.0077700018882751465, "type": "RCTImageView", "updateQueue": null}, "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "memoizedState": null, "mode": 3, "pendingProps": {"accessibilityLabel": undefined, "accessibilityLabelledBy": undefined, "accessibilityState": [Object], "accessible": undefined, "defaultSource": null, "headers": undefined, "loadingIndicatorSrc": null, "onError": [Function handleImageError], "ref": [Function anonymous], "resizeMode": "cover", "shouldNotifyLoadEvents": true, "source": [Array], "src": [Array], "style": [Array]}, "ref": [Function anonymous], "refCleanup": undefined, "return": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.3384620025753975, "actualStartTime": 56779979.587229, "alternate": [FiberNode], "child": [Circular], "childLanes": 0, "deletions": null, "dependencies": [Object], "elementType": [Object], "flags": 524289, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": null, "refCleanup": null, "return": [FiberNode], "selfBaseDuration": 0.0366160050034523, "sibling": null, "stateNode": null, "subtreeFlags": 2097156, "tag": 9, "treeBaseDuration": 0.044307999312877655, "type": [Object], "updateQueue": null}, "selfBaseDuration": 0.007691994309425354, "sibling": null, "stateNode": {"canonical": [Object], "node": [Object]}, "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007691994309425354, "type": "RCTImageView", "updateQueue": null}, "bubbles": undefined, "cancelable": undefined, "currentTarget": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.2969229966402054, "actualStartTime": 56779979.625306, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.007691994309425354, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007691994309425354, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 666, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "defaultPrevented": undefined, "dispatchConfig": {"registrationName": "onError"}, "eventPhase": undefined, "isDefaultPrevented": [Function functionThatReturnsFalse], "isPropagationStopped": [Function functionThatReturnsFalse], "isTrusted": undefined, "nativeEvent": {"error": "unknown image format", "target": 666}, "target": {"__internalInstanceHandle": {"_debugHookTypes": null, "_debugInfo": null, "_debugNeedsRemount": false, "_debugOwner": [FiberNode], "actualDuration": 0.2969229966402054, "actualStartTime": 56779979.625306, "alternate": [FiberNode], "child": null, "childLanes": 0, "deletions": null, "dependencies": null, "elementType": "RCTImageView", "flags": 2097156, "index": 0, "key": null, "lanes": 0, "memoizedProps": [Object], "memoizedState": null, "mode": 3, "pendingProps": [Object], "ref": [Function anonymous], "refCleanup": undefined, "return": [FiberNode], "selfBaseDuration": 0.007691994309425354, "sibling": null, "stateNode": [Object], "subtreeFlags": 0, "tag": 5, "treeBaseDuration": 0.007691994309425354, "type": "RCTImageView", "updateQueue": null}, "__nativeTag": 666, "_viewConfig": {"Commands": [Object], "bubblingEventTypes": [Object], "directEventTypes": [Object], "uiViewClassName": "RCTImageView", "validAttributes": [Object]}}, "timeStamp": 1752913388344, "type": undefined}
 LOG  ❌ Failed URL: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us"}
 LOG    - imageError: true
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU"}
 LOG    - imageError: true
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us"}
 LOG    - imageError: true
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU"}
 LOG    - imageError: true
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T08-08-58-003Z-yz4ncx.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDgtMDgtNTgtMDAzWi15ejRuY3guanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.zZUHiPZFCNdf7YbH_6lkeJP5JYwc0_x922f0g8-J-Us"}
 LOG    - imageError: true
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  🖼️ Transaction has image_url: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🧪 Testing image URL access: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG  🖼️ ImagePreview Debug:
 LOG    - imageUri: undefined
 LOG    - imageUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/public/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg
 LOG    - signedUrl: https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU
 LOG    - imageSource: {"uri": "https://cwktsjtowhtzvfjhukhc.supabase.co/storage/v1/object/sign/transaction-images/eb760dc2-c08a-4b37-a541-5103e0ef5732/transaction/2025-07-19T05-55-21-067Z-i7cncr.jpg?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV9kZDQ0NzlhMC1kZmQwLTQ4ZWMtOTY0OS0yZjdiYmI3NGYxZDciLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJ0cmFuc2FjdGlvbi1pbWFnZXMvZWI3NjBkYzItYzA4YS00YjM3LWE1NDEtNTEwM2UwZWY1NzMyL3RyYW5zYWN0aW9uLzIwMjUtMDctMTlUMDUtNTUtMjEtMDY3Wi1pN2NuY3IuanBnIiwiaWF0IjoxNzUyOTEzMzg3LCJleHAiOjE3NTI5MTY5ODd9.jZ1f3SlKG5qdnGPybfw7XITmbjlkK29NWbNgLYd6_uU"}
 LOG    - imageError: true
 LOG  ❌ ImagePreview: No image source or error, showing placeholder
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 LOG  ✅ Image URL accessible
 LOG  📊 Response status: 200
 LOG  📋 Content type: image/jpeg
 INFO  Launching DevTools...