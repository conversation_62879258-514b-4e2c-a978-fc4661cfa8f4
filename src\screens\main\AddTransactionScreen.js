import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';
import ImagePreview from '../../components/ImagePreview';
import { ImageCompressionService } from '../../services/imageCompressionService';
import { TransactionService } from '../../services/transactionService';
import { useAuth } from '../../context/AuthContext';

export default function AddTransactionScreen({ navigation, route }) {
  const { t } = useTranslation();
  const { hasFeatureAccess } = useAuth();
  const { cashBoxId } = route.params || {};

  // Form state
  const [formData, setFormData] = useState({
    cashBoxId: cashBoxId || '',
    expenseCategoryId: '',
    amount: '',
    description: '',
  });

  // Data state
  const [cashBoxes, setCashBoxes] = useState([]);
  const [expenseCategories, setExpenseCategories] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);

  // UI state
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [showCashBoxPicker, setShowCashBoxPicker] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (formData.cashBoxId) {
      loadExpenseCategories(formData.cashBoxId);
    }
  }, [formData.cashBoxId]);

  const loadInitialData = async () => {
    try {
      setLoadingData(true);
      const boxes = await TransactionService.getCashBoxes();
      setCashBoxes(boxes);

      // If no cash box provided and only one exists, select it
      if (!cashBoxId && boxes.length === 1) {
        setFormData(prev => ({ ...prev, cashBoxId: boxes[0].id }));
      }
    } catch (error) {
      console.error('Error loading cash boxes:', error);
      Alert.alert('Error', 'Failed to load cash boxes');
    } finally {
      setLoadingData(false);
    }
  };

  const loadExpenseCategories = async (boxId) => {
    try {
      const categories = await TransactionService.getExpenseCategories(boxId);
      setExpenseCategories(categories);

      // Reset category selection when cash box changes
      setFormData(prev => ({ ...prev, expenseCategoryId: '' }));
    } catch (error) {
      console.error('Error loading expense categories:', error);
      Alert.alert('Error', 'Failed to load expense categories');
    }
  };

  const handleImageSelection = async (source) => {
    try {
      let result;
      if (source === 'camera') {
        result = await ImageCompressionService.selectFromCamera();
      } else {
        result = await ImageCompressionService.selectFromGallery();
      }

      if (result.success) {
        setSelectedImage({
          uri: result.compressedImage.uri,
          originalSize: result.originalSize,
          compressedSize: result.compressedSize,
          compressionRatio: result.compressionRatio,
        });
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const showImagePicker = () => {
    Alert.alert(
      'Add Image',
      'Choose how to add an image',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Camera', onPress: () => handleImageSelection('camera') },
        { text: 'Gallery', onPress: () => handleImageSelection('gallery') },
      ]
    );
  };

  const removeImage = () => {
    setSelectedImage(null);
  };

  const validateForm = () => {
    if (!formData.cashBoxId) {
      Alert.alert('Error', 'Please select a cash box');
      return false;
    }

    if (!formData.expenseCategoryId) {
      Alert.alert('Error', 'Please select an expense category');
      return false;
    }

    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const transactionData = {
        cashBoxId: formData.cashBoxId,
        expenseCategoryId: formData.expenseCategoryId,
        amount: formData.amount,
        description: formData.description.trim() || null,
        imageUri: selectedImage?.uri || null,
      };

      const result = await TransactionService.createTransaction(transactionData);

      if (result.success) {
        Alert.alert(
          'Success',
          'Transaction recorded successfully!',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack(),
            },
          ]
        );
      } else {
        Alert.alert('Error', result.error);
      }
    } catch (error) {
      console.error('Error creating transaction:', error);
      Alert.alert('Error', 'Failed to create transaction. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getCashBoxName = (id) => {
    const box = cashBoxes.find(b => b.id === id);
    return box ? box.name : 'Select Cash Box';
  };

  const getCategoryName = (id) => {
    const category = expenseCategories.find(c => c.id === id);
    return category ? category.name : 'Select Category';
  };

  if (loadingData) {
    return (
      <View style={styles.container}>
        <AppHeader />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AppHeader />

      <KeyboardAvoidingView
        style={globalStyles.screenContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Add Transaction</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Form Container */}
          <View style={styles.formContainer}>

            {/* Cash Box Selection */}
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Cash Box *</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => setShowCashBoxPicker(!showCashBoxPicker)}
              >
                <Text style={[styles.pickerText, !formData.cashBoxId && styles.placeholderText]}>
                  {getCashBoxName(formData.cashBoxId)}
                </Text>
                <Icon name="arrow-drop-down" size={24} color={colors.text} />
              </TouchableOpacity>

              {showCashBoxPicker && (
                <View style={styles.pickerContainer}>
                  {cashBoxes.map((box) => (
                    <TouchableOpacity
                      key={box.id}
                      style={styles.pickerItem}
                      onPress={() => {
                        setFormData(prev => ({ ...prev, cashBoxId: box.id }));
                        setShowCashBoxPicker(false);
                      }}
                    >
                      <Text style={styles.pickerItemText}>{box.name}</Text>
                      <Text style={styles.pickerItemSubtext}>
                        Balance: ₹{box.current_balance?.toFixed(2) || '0.00'}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>

            {/* Expense Category Selection */}
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Expense Category *</Text>
              <TouchableOpacity
                style={[styles.pickerButton, !formData.cashBoxId && styles.disabledButton]}
                onPress={() => formData.cashBoxId && setShowCategoryPicker(!showCategoryPicker)}
                disabled={!formData.cashBoxId}
              >
                <Text style={[styles.pickerText, !formData.expenseCategoryId && styles.placeholderText]}>
                  {getCategoryName(formData.expenseCategoryId)}
                </Text>
                <Icon name="arrow-drop-down" size={24} color={colors.text} />
              </TouchableOpacity>

              {showCategoryPicker && (
                <View style={styles.pickerContainer}>
                  {expenseCategories.map((category) => (
                    <TouchableOpacity
                      key={category.id}
                      style={styles.pickerItem}
                      onPress={() => {
                        setFormData(prev => ({ ...prev, expenseCategoryId: category.id }));
                        setShowCategoryPicker(false);
                      }}
                    >
                      <Text style={styles.pickerItemText}>{category.name}</Text>
                      {category.is_predefined && (
                        <Text style={styles.predefinedBadge}>Default</Text>
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>

            {/* Amount Input */}
            <View style={styles.fieldContainer}>
              <Text style={styles.fieldLabel}>Amount *</Text>
              <View style={styles.amountContainer}>
                <Text style={styles.currencySymbol}>₹</Text>
                <TextInput
                  style={styles.amountInput}
                  value={formData.amount}
                  onChangeText={(text) => {
                    // Only allow numbers and decimal point
                    const cleanText = text.replace(/[^0-9.]/g, '');
                    // Prevent multiple decimal points
                    const parts = cleanText.split('.');
                    if (parts.length > 2) {
                      return;
                    }
                    setFormData(prev => ({ ...prev, amount: cleanText }));
                  }}
                  placeholder="0.00"
                  placeholderTextColor={colors.placeholder}
                  keyboardType="decimal-pad"
                  returnKeyType="next"
                />
              </View>
            </View>

            {/* Description Input (Premium Feature) */}
            {hasFeatureAccess('description_field') && (
              <View style={styles.fieldContainer}>
                <Text style={styles.fieldLabel}>Description (Optional)</Text>
                <TextInput
                  style={styles.textInput}
                  value={formData.description}
                  onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
                  placeholder="Add details about this expense..."
                  placeholderTextColor={colors.placeholder}
                  multiline
                  numberOfLines={3}
                  textAlignVertical="top"
                />
              </View>
            )}

            {/* Image Upload (Premium Feature) */}
            {hasFeatureAccess('image_upload') && (
              <View style={styles.fieldContainer}>
                <Text style={styles.fieldLabel}>Receipt/Bill Image (Optional)</Text>

                {selectedImage ? (
                  <View style={styles.imageContainer}>
                    <ImagePreview
                      imageUri={selectedImage.uri}
                      thumbnailSize={120}
                      showDeleteButton={true}
                      onDelete={removeImage}
                    />
                    <View style={styles.imageInfo}>
                      <Text style={styles.imageInfoText}>
                        Original: {(selectedImage.originalSize / 1024 / 1024).toFixed(2)} MB
                      </Text>
                      <Text style={styles.imageInfoText}>
                        Compressed: {(selectedImage.compressedSize / 1024).toFixed(0)} KB
                      </Text>
                      <Text style={styles.imageInfoText}>
                        Saved: {selectedImage.compressionRatio.toFixed(1)}%
                      </Text>
                    </View>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={styles.imageUploadButton}
                    onPress={showImagePicker}
                  >
                    <Icon name="add-a-photo" size={32} color={colors.primary} />
                    <Text style={styles.imageUploadText}>Add Receipt Image</Text>
                    <Text style={styles.imageUploadSubtext}>
                      Take photo or select from gallery
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {/* Submit Button */}
            <TouchableOpacity
              style={[globalStyles.primaryButton, loading && styles.disabledButton]}
              onPress={handleSubmit}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color={colors.background} />
              ) : (
                <Text style={globalStyles.buttonText}>Record Transaction</Text>
              )}
            </TouchableOpacity>

          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.text,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface,
  },
  placeholder: {
    width: 40,
  },
  scrollContainer: {
    paddingBottom: 27, // Global 27px margin bottom
  },
  formContainer: {
    padding: 16,
  },
  fieldContainer: {
    marginBottom: 20,
  },
  fieldLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.onSurface,
    marginBottom: 8,
  },
  pickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    padding: 16,
    minHeight: 56,
  },
  disabledButton: {
    opacity: 0.5,
  },
  pickerText: {
    fontSize: 16,
    color: colors.text,
    flex: 1,
  },
  placeholderText: {
    color: colors.placeholder,
  },
  pickerContainer: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    marginTop: 4,
    maxHeight: 200,
  },
  pickerItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  pickerItemText: {
    fontSize: 16,
    color: colors.text,
    flex: 1,
  },
  pickerItemSubtext: {
    fontSize: 14,
    color: colors.placeholder,
  },
  predefinedBadge: {
    fontSize: 12,
    color: colors.primary,
    backgroundColor: colors.primaryLight,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    paddingHorizontal: 16,
    minHeight: 56,
  },
  currencySymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginRight: 8,
  },
  amountInput: {
    flex: 1,
    fontSize: 18,
    color: colors.text,
    padding: 0,
  },
  textInput: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4,
    padding: 16,
    fontSize: 16,
    color: colors.text,
    minHeight: 80,
  },
  imageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  imageInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  imageInfoText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
  },
  imageUploadButton: {
    backgroundColor: colors.surface,
    borderWidth: 2,
    borderColor: colors.border,
    borderStyle: 'dashed',
    borderRadius: 4,
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageUploadText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary,
    marginTop: 8,
  },
  imageUploadSubtext: {
    fontSize: 14,
    color: colors.placeholder,
    marginTop: 4,
    textAlign: 'center',
  },
});