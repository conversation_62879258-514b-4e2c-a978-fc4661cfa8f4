import { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { globalStyles } from '../../constants/styles';
import { useAuth } from '../../context/AuthContext';
import { colors } from '../../constants/colors';

export default function HomeScreen() {
  const { userRole, user, hasFeatureAccess, hasRole } = useAuth();

  useEffect(() => {
    // Log role information for debugging
    console.log('=== ROLE CHECK ===');
    console.log('Current user role:', userRole);
    console.log('User email:', user?.email);
    console.log('Is superadmin:', hasRole('superadmin'));
    console.log('Can manage roles:', hasFeatureAccess('role_management'));
    console.log('==================');
  }, [userRole, user]);

  return (
    <View style={globalStyles.container}>
      <Text style={styles.title}>KharchaPani Dashboard</Text>

      {/* Temporary Role Display */}
      <View style={styles.roleCard}>
        <Text style={styles.roleTitle}>Current User Info</Text>
        <Text style={styles.roleText}>Email: {user?.email}</Text>
        <Text style={styles.roleText}>Role: {userRole || 'Loading...'}</Text>
        <Text style={styles.roleText}>
          Status: {hasRole('superadmin') ? '👑 SUPERADMIN' : '👤 Regular User'}
        </Text>
      </View>

      <Text style={styles.subtitle}>Home Screen - To be implemented</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text, // Global text color
    textAlign: 'center',
    marginTop: 20,
  },
  roleCard: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: colors.border,
  },
  roleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    marginBottom: 8,
  },
  roleText: {
    fontSize: 14,
    color: colors.text, // Global text color
    marginBottom: 4,
  },
});
