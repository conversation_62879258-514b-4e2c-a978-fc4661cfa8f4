import { useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { globalStyles } from '../../constants/styles';
import { useAuth } from '../../context/AuthContext';
import { colors } from '../../constants/colors';
import AppHeader from '../../components/AppHeader';
import { USER_TYPES } from '../../utils/loginDetection';
import { useTranslation } from 'react-i18next';

export default function HomeScreen({ navigation }) {
  const { userRole, user, hasFeatureAccess, hasRole, userType, custodianSession } = useAuth();
  const { t } = useTranslation();

  useEffect(() => {
    // Log role information for debugging
    console.log('=== ROLE CHECK ===');
    console.log('Current user role:', userRole);
    console.log('User email:', user?.email);
    console.log('Is superadmin:', hasRole('superadmin'));
    console.log('Can manage roles:', hasFeatureAccess('role_management'));
    console.log('==================');
  }, [userRole, user]);

  return (
    <View style={styles.container}>
      {/* Global Header */}
      <AppHeader />

      <View style={globalStyles.screenContainer}>
        <Text style={styles.title}>{t('appName', 'खर्चापानी')} {t('dashboard', 'डैशबोर्ड')}</Text>

      {/* Temporary Role Display */}
      <View style={styles.roleCard}>
        <Text style={styles.roleTitle}>{t('currentUserInfo', 'Current User Info')}</Text>
        {userType === USER_TYPES.CUSTODIAN ? (
          <Text style={styles.roleText}>
            {t('mobileNo', 'Mobile No')}: {custodianSession?.phone}
          </Text>
        ) : (
          <Text style={styles.roleText}>Email: {user?.email}</Text>
        )}
        <Text style={styles.roleText}>{t('role', 'Role')}: {userRole === 'custodian' ? t('custodian', 'संरक्षक') : userRole || 'Loading...'}</Text>
        <Text style={styles.roleText}>
          {t('status', 'Status')}: {hasRole('superadmin') ? '👑 SUPERADMIN' : `👤 ${t('regularUser', 'Regular User')}`}
        </Text>
      </View>

        {/* Quick Actions for Issuers */}
        {hasFeatureAccess('manage_custodians') && (
          <View style={styles.quickActions}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>

            <TouchableOpacity
              style={styles.actionCard}
              onPress={() => navigation.navigate('ManageCustodians')}
            >
              <Icon name="people" size={24} color={colors.primary} />
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Manage Custodians</Text>
                <Text style={styles.actionSubtitle}>Add or manage your custodians</Text>
              </View>
              <Icon name="chevron-right" size={20} color={colors.placeholder} />
            </TouchableOpacity>
          </View>
        )}

        <Text style={styles.subtitle}>{t('homeScreenPlaceholder', 'होम स्क्रीन - लागू किया जाना है')}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text, // Global text color
    textAlign: 'center',
    marginTop: 20,
  },
  roleCard: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: colors.border,
  },
  roleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    marginBottom: 8,
  },
  roleText: {
    fontSize: 14,
    color: colors.text, // Global text color
    marginBottom: 4,
  },
  quickActions: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    marginBottom: 12,
    paddingHorizontal: 16,
  },
  actionCard: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  actionContent: {
    flex: 1,
    marginLeft: 12,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.onSurface, // Global heading color
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 14,
    color: colors.text, // Global text color
  },
});
