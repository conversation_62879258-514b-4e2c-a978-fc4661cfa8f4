import { supabase } from '../lib/supabase';

/**
 * Setup Storage Buckets
 * Creates necessary storage buckets in Supabase
 * Run this once to initialize storage infrastructure
 */

export const setupStorageBuckets = async () => {
  try {
    console.log('🚀 Setting up Supabase storage buckets...');

    // Define buckets to create
    const buckets = [
      {
        name: 'transaction-images',
        options: {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/jpg'],
          fileSizeLimit: 1024 * 1024, // 1MB limit
        }
      },
      {
        name: 'voice-memos',
        options: {
          public: false, // Voice memos should be private
          allowedMimeTypes: ['audio/mp4', 'audio/mpeg', 'audio/wav'],
          fileSizeLimit: 512 * 1024, // 512KB limit for voice memos
        }
      },
      {
        name: 'profile-pictures',
        options: {
          public: true,
          allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png'],
          fileSizeLimit: 512 * 1024, // 512KB limit for profile pictures
        }
      }
    ];

    // Create each bucket
    for (const bucket of buckets) {
      try {
        console.log(`📦 Creating bucket: ${bucket.name}`);
        
        const { data, error } = await supabase.storage.createBucket(
          bucket.name,
          bucket.options
        );

        if (error) {
          if (error.message.includes('already exists') || error.message.includes('Duplicate')) {
            console.log(`✅ Bucket ${bucket.name} already exists`);
          } else {
            console.error(`❌ Error creating bucket ${bucket.name}:`, {
              message: error.message,
              statusCode: error.statusCode,
              error: error.error,
              details: error.details,
            });
          }
        } else {
          console.log(`✅ Bucket ${bucket.name} created successfully`, data);
        }
      } catch (bucketError) {
        console.error(`❌ Exception creating bucket ${bucket.name}:`, bucketError);
      }
    }

    console.log('🎉 Storage setup complete!');
    return true;

  } catch (error) {
    console.error('❌ Error setting up storage:', error);
    return false;
  }
};

/**
 * Test storage connectivity and public URL access
 */
export const testStorageConnection = async () => {
  try {
    console.log('🧪 Testing storage connection...');

    // List buckets to test connection
    const { data, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('❌ Storage connection test failed:', error);
      return false;
    }

    console.log('✅ Storage connection successful');
    console.log('📦 Available buckets:', data.map(b => `${b.name} (public: ${b.public})`));

    // Test public URL generation
    const testUrl = supabase.storage
      .from('transaction-images')
      .getPublicUrl('test-file.jpg');

    console.log('🔗 Test public URL format:', testUrl.data.publicUrl);

    return true;

  } catch (error) {
    console.error('❌ Storage connection test error:', error);
    return false;
  }
};

/**
 * Get storage bucket info
 */
export const getStorageInfo = async () => {
  try {
    const { data, error } = await supabase.storage.listBuckets();

    if (error) {
      throw error;
    }

    return data.map(bucket => ({
      name: bucket.name,
      id: bucket.id,
      public: bucket.public,
      createdAt: bucket.created_at,
      updatedAt: bucket.updated_at,
    }));

  } catch (error) {
    console.error('Error getting storage info:', error);
    return [];
  }
};

/**
 * Test image URL accessibility
 */
export const testImageAccess = async (imageUrl) => {
  try {
    console.log('🧪 Testing image URL access:', imageUrl);

    // Try to fetch the image
    const response = await fetch(imageUrl);

    if (response.ok) {
      console.log('✅ Image URL accessible');
      console.log('📊 Response status:', response.status);
      console.log('📋 Content type:', response.headers.get('content-type'));
      return true;
    } else {
      console.error('❌ Image URL not accessible');
      console.error('📊 Response status:', response.status);
      console.error('📋 Response text:', await response.text());
      return false;
    }

  } catch (error) {
    console.error('❌ Error testing image access:', error);
    return false;
  }
};
