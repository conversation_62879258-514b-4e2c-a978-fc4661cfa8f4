-- =====================================================
-- Fixed Transaction Details View
-- =====================================================
-- This fixes the view with correct custodian table structure

-- Drop and recreate the view with correct custodian join
DROP VIEW IF EXISTS transaction_details;

CREATE OR REPLACE VIEW transaction_details AS
SELECT 
  t.id,
  t.cash_box_id,
  t.amount,
  t.description,
  t.image_url,
  t.image_filename,
  t.voice_memo_url,
  t.voice_memo_filename,
  t.transaction_date,
  t.created_at,
  
  -- Cash box info
  cb.name as cash_box_name,
  cb.issuer_user_id,
  
  -- Category info
  ec.name as category_name,
  
  -- Custodian info (using auth.users directly since custodians might not have user_id)
  COALESCE(c.full_name, u.email) as custodian_name,
  c.phone as custodian_phone,
  t.custodian_user_id
  
FROM transactions t
JOIN cash_boxes cb ON t.cash_box_id = cb.id
JOIN expense_categories ec ON t.expense_category_id = ec.id
LEFT JOIN auth.users u ON t.custodian_user_id = u.id
LEFT JOIN custodians c ON c.issuer_user_id = cb.issuer_user_id;

-- Grant permissions
GRANT SELECT ON transaction_details TO authenticated;
