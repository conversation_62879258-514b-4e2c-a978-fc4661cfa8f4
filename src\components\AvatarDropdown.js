import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../context/AuthContext';
import { colors } from '../constants/colors';

const { width: screenWidth } = Dimensions.get('window');

/**
 * Avatar Dropdown Component
 * Shows a dropdown menu when avatar is pressed
 */
export default function AvatarDropdown() {
  const { user, userRole, userType, logout } = useAuth();
  const [isVisible, setIsVisible] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0 });
  const avatarRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const handleAvatarPress = () => {
    // Measure avatar position for dropdown placement
    avatarRef.current?.measure((x, y, width, height, pageX, pageY) => {
      setDropdownPosition({
        top: pageY + height + 5, // Position below avatar
        right: screenWidth - pageX - width, // Align to right edge of avatar
      });
      setIsVisible(true);
      
      // Animate dropdown appearance
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    });
  };

  const hideDropdown = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 150,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
    });
  };

  const handleProfile = () => {
    hideDropdown();
    // TODO: Navigate to profile screen
    Alert.alert('Profile', 'Profile screen coming soon!');
  };

  const handleLogout = async () => {
    hideDropdown();
    
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await logout();
              if (!result.success) {
                Alert.alert('Error', result.error || 'Failed to logout');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          },
        },
      ]
    );
  };

  const getUserInitials = () => {
    if (user?.user_metadata?.full_name) {
      return user.user_metadata.full_name
        .split(' ')
        .map(name => name.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    }
    if (user?.email) {
      return user.email.charAt(0).toUpperCase();
    }
    return 'U';
  };

  const getRoleDisplay = () => {
    switch (userRole) {
      case 'superadmin':
        return '👑 Superadmin';
      case 'admin':
        return '👨‍💼 Admin';
      case 'premium_issuer':
        return '⭐ Premium Issuer';
      case 'issuer':
        return '👨‍💼 Issuer';
      case 'custodian':
        return '👷‍♂️ Custodian';
      default:
        return '👤 User';
    }
  };

  return (
    <>
      {/* Avatar Button */}
      <TouchableOpacity
        ref={avatarRef}
        style={styles.avatar}
        onPress={handleAvatarPress}
        activeOpacity={0.7}
      >
        <Text style={styles.avatarText}>{getUserInitials()}</Text>
      </TouchableOpacity>

      {/* Dropdown Modal */}
      <Modal
        visible={isVisible}
        transparent={true}
        animationType="none"
        onRequestClose={hideDropdown}
      >
        <TouchableOpacity
          style={styles.overlay}
          activeOpacity={1}
          onPress={hideDropdown}
        >
          <Animated.View
            style={[
              styles.dropdown,
              {
                top: dropdownPosition.top,
                right: dropdownPosition.right,
                opacity: fadeAnim,
                transform: [
                  {
                    scale: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1],
                    }),
                  },
                ],
              },
            ]}
          >
            {/* User Info Header */}
            <View style={styles.userInfo}>
              <View style={styles.userDetails}>
                <Text style={styles.userName} numberOfLines={1}>
                  {user?.user_metadata?.full_name || user?.email || 'User'}
                </Text>
                <Text style={styles.userRole}>{getRoleDisplay()}</Text>
              </View>
            </View>

            {/* Divider */}
            <View style={styles.divider} />

            {/* Menu Items */}
            <TouchableOpacity style={styles.menuItem} onPress={handleProfile}>
              <Icon name="person" size={20} color={colors.text} />
              <Text style={styles.menuText}>Profile</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.menuItem} onPress={handleLogout}>
              <Icon name="logout" size={20} color={colors.error} />
              <Text style={[styles.menuText, { color: colors.error }]}>Logout</Text>
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  avatar: {
    width: 28, // More compact size
    height: 28, // More compact size
    borderRadius: 4, // Global 4px border radius (square avatar)
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: colors.background,
    fontSize: 12,
    fontWeight: 'bold',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  dropdown: {
    position: 'absolute',
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  userInfo: {
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    marginBottom: 2,
  },
  userRole: {
    fontSize: 12,
    color: colors.text, // Global text color
  },
  divider: {
    height: 1,
    backgroundColor: colors.border,
    marginHorizontal: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  menuText: {
    fontSize: 14,
    color: colors.text, // Global text color
    marginLeft: 12,
    fontWeight: '500',
  },
});
