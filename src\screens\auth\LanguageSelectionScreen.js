import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { useTranslation } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';

export default function LanguageSelectionScreen({ navigation }) {
  const { i18n } = useTranslation();

  // For testing - clear language preference
  const clearLanguagePreference = async () => {
    try {
      await AsyncStorage.removeItem('user_language_preference');
      console.log('Language preference cleared for testing');
    } catch (error) {
      console.error('Error clearing language preference:', error);
    }
  };

  const handleLanguageSelect = async (languageCode) => {
    try {
      // Change the language
      await i18n.changeLanguage(languageCode);

      // Store the language preference
      await AsyncStorage.setItem('user_language_preference', languageCode);

      // Navigate to auth flow
      navigation.navigate('Auth', { screen: 'Login' });
    } catch (error) {
      console.error('Error setting language:', error);
      // Still navigate even if storage fails
      navigation.navigate('Auth', { screen: 'Login' });
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <AppHeader showNotification={false} showAvatar={false} />

      <View style={globalStyles.screenContainer}>
        {/* Subtitle */}
        <View style={styles.subtitleContainer}>
          <Text style={styles.subtitle}>
            Record and Manage your Petty Cash Transactions on Project Sites efficiently
          </Text>
        </View>

        {/* Language Selection */}
        <View style={styles.languageContainer}>
          <Text style={styles.title}>Please Choose your Language</Text>
          <Text style={styles.titleHindi}>अपनी भाषा चुनिए</Text>

          <View style={styles.buttonContainer}>
            {/* English Button */}
            <TouchableOpacity
              onPress={() => handleLanguageSelect('en')}
            >
              <View style={styles.flagContainer}>
                <Text style={styles.flagText}>English</Text>
              </View>
            </TouchableOpacity>

            {/* Hindi Button */}
            <TouchableOpacity
              onPress={() => handleLanguageSelect('hi')}
            >
              <View style={styles.flagContainer}>
                <Text style={styles.flagText}>हिंदी</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  subtitleContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.surface,
    borderRadius: 4,
    marginBottom: 40,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
    lineHeight: 22,
  },
  languageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  titleHindi: {
    fontSize: 20,
    fontWeight: '500',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 40,
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
  },
  flagContainer: {
    width: 120,
    height: 40,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    borderRadius: 4,
    paddingHorizontal: 10,
  },
  flagText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.background,
  },
  languageText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
});
