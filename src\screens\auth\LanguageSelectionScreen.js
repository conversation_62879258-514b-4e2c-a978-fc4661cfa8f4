import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { useTranslation } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';

export default function LanguageSelectionScreen({ navigation }) {
  const { i18n } = useTranslation();

  // For testing - clear language preference
  const clearLanguagePreference = async () => {
    try {
      await AsyncStorage.removeItem('user_language_preference');
      console.log('Language preference cleared for testing');
    } catch (error) {
      console.error('Error clearing language preference:', error);
    }
  };

  const handleLanguageSelect = async (languageCode) => {
    try {
      // Change the language
      await i18n.changeLanguage(languageCode);

      // Store the language preference
      await AsyncStorage.setItem('user_language_preference', languageCode);

      // Navigate to auth flow
      navigation.navigate('Auth', { screen: 'Login' });
    } catch (error) {
      console.error('Error setting language:', error);
      // Still navigate even if storage fails
      navigation.navigate('Auth', { screen: 'Login' });
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <AppHeader showNotification={false} showAvatar={false} />

      <View style={globalStyles.screenContainer}>
        {/* Subtitle */}
        <View style={styles.subtitleContainer}>
          <Text style={styles.subtitle}>
            Record and Manage your Petty Cash Transactions on Project Sites efficiently
          </Text>
        </View>

        {/* Language Selection */}
        <View style={styles.languageContainer}>
          <Text style={styles.title}>Please Choose your Language</Text>
          <Text style={styles.titleHindi}>अपनी भाषा चुनिए</Text>

          <View style={styles.buttonContainer}>
            {/* English Button */}
            <TouchableOpacity
              style={styles.languageButton}
              onPress={() => handleLanguageSelect('en')}
            >
              <View style={styles.flagContainer}>
                <Text style={styles.flagText}>EN</Text>
              </View>
              <Text style={styles.languageText}>English</Text>
            </TouchableOpacity>

            {/* Hindi Button */}
            <TouchableOpacity
              style={styles.languageButton}
              onPress={() => handleLanguageSelect('hi')}
            >
              <View style={styles.flagContainer}>
                <Text style={styles.flagText}>हि</Text>
              </View>
              <Text style={styles.languageText}>हिंदी</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  subtitleContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.surface,
    borderRadius: 4,
    marginBottom: 40,
  },
  subtitle: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
    lineHeight: 22,
  },
  languageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.onSurface,
    textAlign: 'center',
    marginBottom: 8,
  },
  titleHindi: {
    fontSize: 20,
    fontWeight: '500',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 40,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
  },
  languageButton: {
    backgroundColor: colors.background,
    borderWidth: 2,
    borderColor: colors.primary,
    borderRadius: 4,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    minWidth: 120,
    elevation: 2,
    shadowColor: colors.secondary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  flagContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  flagText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.background,
  },
  languageText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.onSurface,
  },
});
