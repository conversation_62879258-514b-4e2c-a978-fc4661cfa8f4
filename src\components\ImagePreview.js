import React, { useState, useEffect } from 'react';
import {
  View,
  Image,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  Alert,
  Text,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../constants/colors';
import { supabase } from '../lib/supabase';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

/**
 * ImagePreview Component
 * Displays image thumbnails with full-screen preview and optional delete functionality
 */
export default function ImagePreview({
  imageUri,
  imageUrl,
  onDelete,
  onPress,
  style,
  thumbnailSize = 80,
  showDeleteButton = false,
  showFullscreenButton = true,
  borderRadius = 4,
}) {
  const [modalVisible, setModalVisible] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [signedUrl, setSignedUrl] = useState(null);

  // Generate signed URL for Supabase images
  useEffect(() => {
    const generateSignedUrl = async () => {
      if (imageUrl && imageUrl.includes('supabase.co/storage')) {
        try {
          // Extract file path from public URL
          const urlParts = imageUrl.split('/storage/v1/object/public/transaction-images/');
          if (urlParts.length === 2) {
            const filePath = urlParts[1];

            console.log('🔐 Generating signed URL for:', filePath);

            const { data, error } = await supabase.storage
              .from('transaction-images')
              .createSignedUrl(filePath, 3600); // 1 hour expiry

            if (error) {
              console.error('❌ Error generating signed URL:', error);
              setImageError(true);
            } else {
              console.log('✅ Signed URL generated:', data.signedUrl);
              setSignedUrl(data.signedUrl);
            }
          }
        } catch (error) {
          console.error('❌ Exception generating signed URL:', error);
          setImageError(true);
        }
      }
    };

    generateSignedUrl();
  }, [imageUrl]);

  // Use imageUri (local), signedUrl (remote signed), or imageUrl (remote public)
  const imageSource = imageUri
    ? { uri: imageUri }
    : signedUrl
    ? { uri: signedUrl }
    : imageUrl
    ? { uri: imageUrl }
    : null;

  // Debug logging (reduced)
  if (imageError) {
    console.log('❌ ImagePreview Error:', { imageUri, imageUrl, signedUrl, imageError });
  }

  if (!imageSource || imageError) {
    // Temporarily hide broken images to focus on new uploads
    return (
      <View style={[styles.placeholder, { width: thumbnailSize, height: thumbnailSize, borderRadius }, style]}>
        <Icon name="image" size={24} color={colors.placeholder} />
        <Text style={styles.placeholderText}>Image</Text>
      </View>
    );
  }

  const handleImageError = (error) => {
    console.log('❌ Image load error:', error);
    console.log('❌ Failed URL:', imageUrl || imageUri);
    setImageError(true);
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Image',
      'Are you sure you want to delete this image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onDelete && onDelete(),
        },
      ]
    );
  };

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else if (showFullscreenButton) {
      setModalVisible(true);
    }
  };

  return (
    <>
      {/* Thumbnail */}
      <View style={[styles.container, style]}>
        <TouchableOpacity
          style={[
            styles.thumbnail,
            {
              width: thumbnailSize,
              height: thumbnailSize,
              borderRadius,
            },
          ]}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          <Image
            source={imageSource}
            style={[
              styles.image,
              {
                width: thumbnailSize,
                height: thumbnailSize,
                borderRadius,
              },
            ]}
            resizeMode="cover"
            onError={handleImageError}
          />
          
          {/* Overlay for fullscreen indicator */}
          {showFullscreenButton && (
            <View style={styles.overlay}>
              <Icon name="fullscreen" size={16} color="white" />
            </View>
          )}
        </TouchableOpacity>

        {/* Delete button */}
        {showDeleteButton && onDelete && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDelete}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Icon name="close" size={16} color="white" />
          </TouchableOpacity>
        )}
      </View>

      {/* Full-screen modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalBackground}
            activeOpacity={1}
            onPress={() => setModalVisible(false)}
          >
            <View style={styles.modalContent}>
              <Image
                source={imageSource}
                style={styles.fullscreenImage}
                resizeMode="contain"
              />
              
              {/* Close button */}
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setModalVisible(false)}
              >
                <Icon name="close" size={24} color="white" />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  thumbnail: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: colors.error,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.background,
  },
  placeholder: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 10,
    color: colors.placeholder,
    marginTop: 4,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenImage: {
    width: screenWidth - 40,
    height: screenHeight - 100,
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

/**
 * ImageGrid Component
 * Displays multiple images in a grid layout
 */
export function ImageGrid({
  images = [],
  onImagePress,
  onImageDelete,
  maxImages = 3,
  thumbnailSize = 80,
  showDeleteButtons = false,
  style,
}) {
  const displayImages = images.slice(0, maxImages);
  const remainingCount = Math.max(0, images.length - maxImages);

  return (
    <View style={[styles.gridContainer, style]}>
      {displayImages.map((image, index) => (
        <ImagePreview
          key={index}
          imageUri={image.uri}
          imageUrl={image.url}
          thumbnailSize={thumbnailSize}
          showDeleteButton={showDeleteButtons}
          onDelete={() => onImageDelete && onImageDelete(index)}
          onPress={() => onImagePress && onImagePress(index)}
          style={styles.gridItem}
        />
      ))}
      
      {/* Show remaining count if there are more images */}
      {remainingCount > 0 && (
        <View
          style={[
            styles.remainingCount,
            {
              width: thumbnailSize,
              height: thumbnailSize,
              borderRadius: 4,
            },
          ]}
        >
          <Text style={styles.remainingText}>+{remainingCount}</Text>
        </View>
      )}
    </View>
  );
}

const gridStyles = StyleSheet.create({
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  gridItem: {
    marginRight: 8,
    marginBottom: 8,
  },
  remainingCount: {
    backgroundColor: colors.surface,
    borderWidth: 1,
    borderColor: colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  remainingText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.text,
  },
});

// Merge styles
Object.assign(styles, gridStyles);
