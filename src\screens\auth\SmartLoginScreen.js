import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTranslation } from 'react-i18next';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../../context/AuthContext';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import AppHeader from '../../components/AppHeader';
import {
  detectUserType,
  getIdentifierPlaceholder,
  getCredentialPlaceholder,
  getKeyboardType,
  getCredentialMaxLength,
  getHintText,
  validateLoginInput,
  USER_TYPES
} from '../../utils/loginDetection';

export default function SmartLoginScreen({ navigation }) {
  const { t } = useTranslation();
  const { login, loginCustodian, loading } = useAuth();

  // TEMPORARY: Reset language preference for testing
  const resetLanguagePreference = async () => {
    try {
      await AsyncStorage.removeItem('user_language_preference');
      Alert.alert(
        'Language Reset',
        'Language preference cleared! Please restart the app to see the Language Selection Screen.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error clearing language preference:', error);
      Alert.alert('Error', 'Failed to clear language preference');
    }
  };
  
  // Form state
  const [identifier, setIdentifier] = useState(''); // Email or Phone
  const [credential, setCredential] = useState(''); // Password or PIN
  const [showCredential, setShowCredential] = useState(false);
  const [errors, setErrors] = useState({});
  
  // Auto-detected user type
  const [detectedUserType, setDetectedUserType] = useState(USER_TYPES.UNKNOWN);

  // Update detected user type when identifier changes
  useEffect(() => {
    const userType = detectUserType(identifier);
    setDetectedUserType(userType);
    // Clear errors when user type changes
    if (errors.identifier || errors.credential) {
      setErrors({});
    }
  }, [identifier]);

  const handleIdentifierChange = (value) => {
    setIdentifier(value);
  };

  const handleCredentialChange = (value) => {
    setCredential(value);
  };

  const handleLogin = async () => {
    try {
      // Validate input
      const validation = validateLoginInput(identifier, credential, detectedUserType);
      
      if (!validation.isValid) {
        setErrors(validation.errors);
        return;
      }

      setErrors({});

      let result;
      
      if (detectedUserType === USER_TYPES.ISSUER) {
        // Issuer login with email + password
        result = await login(identifier.trim(), credential);
      } else if (detectedUserType === USER_TYPES.CUSTODIAN) {
        // Custodian login with phone + PIN
        result = await loginCustodian(identifier.trim(), credential);
      } else {
        Alert.alert('Error', 'Please enter a valid email address or phone number');
        return;
      }

      if (result.success) {
        // Navigation will be handled by the auth state change
        console.log('Login successful');
      } else {
        Alert.alert('Login Failed', result.error || 'Please check your credentials and try again');
      }
    } catch (error) {
      console.error('Login error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    }
  };

  const getUserTypeDisplay = () => {
    switch (detectedUserType) {
      case USER_TYPES.ISSUER:
        return '👨‍💼 Account Owner';
      case USER_TYPES.CUSTODIAN:
        return '👷‍♂️ Custodian';
      default:
        return '👤 User';
    }
  };

  const getUserTypeColor = () => {
    switch (detectedUserType) {
      case USER_TYPES.ISSUER:
        return colors.primary;
      case USER_TYPES.CUSTODIAN:
        return colors.secondary;
      default:
        return colors.text;
    }
  };

  return (
    <View style={styles.keyboardContainer}>
      {/* Global Header */}
      <AppHeader />
      
      <KeyboardAvoidingView
        style={globalStyles.screenContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Subtitle */}
          <View style={styles.subtitleContainer}>
            <Text style={styles.subtitle}>
              {t('appSubtitle', 'Record and Manage your Petty Cash Transactions on Project Sites efficiently')}
            </Text>
          </View>

          {/* Form Container */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>{t('signIn')}</Text>

            {/* User Type Indicator */}
            {detectedUserType !== USER_TYPES.UNKNOWN && (
              <View style={styles.userTypeIndicator}>
                <Text style={[styles.userTypeText, { color: getUserTypeColor() }]}>
                  {getUserTypeDisplay()}
                </Text>
              </View>
            )}

            {/* Identifier Input (Email or Phone) */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>
                {detectedUserType === USER_TYPES.CUSTODIAN ? t('phoneNumber', 'Phone Number') : t('emailAddress', 'Email Address')}
              </Text>
              <View style={[styles.compactInputWrapper, errors.identifier && styles.inputError]}>
                <Icon 
                  name={detectedUserType === USER_TYPES.CUSTODIAN ? 'phone' : 'email'} 
                  size={18} 
                  color={colors.placeholder} 
                  style={styles.inputIcon} 
                />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder={getIdentifierPlaceholder(detectedUserType, t)}
                  placeholderTextColor={colors.placeholder}
                  value={identifier}
                  onChangeText={handleIdentifierChange}
                  keyboardType={getKeyboardType(detectedUserType, 'identifier')}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>
              {errors.identifier && <Text style={styles.errorText}>{errors.identifier}</Text>}
            </View>

            {/* Credential Input (Password or PIN) */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>
                {detectedUserType === USER_TYPES.CUSTODIAN ? t('pin', 'PIN') : t('password', 'Password')}
              </Text>
              <View style={[styles.compactInputWrapper, errors.credential && styles.inputError]}>
                <Icon name="lock" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder={getCredentialPlaceholder(detectedUserType, t)}
                  placeholderTextColor={colors.placeholder}
                  value={credential}
                  onChangeText={handleCredentialChange}
                  secureTextEntry={!showCredential}
                  keyboardType={getKeyboardType(detectedUserType, 'credential')}
                  maxLength={getCredentialMaxLength(detectedUserType)}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
                <TouchableOpacity
                  onPress={() => setShowCredential(!showCredential)}
                  style={styles.eyeIcon}
                >
                  <Icon
                    name={showCredential ? 'visibility' : 'visibility-off'}
                    size={18}
                    color={colors.placeholder}
                  />
                </TouchableOpacity>
              </View>
              {errors.credential && <Text style={styles.errorText}>{errors.credential}</Text>}
            </View>

            {/* Hint Text */}
            <Text style={styles.hintText}>
              {getHintText(detectedUserType, t)}
            </Text>

            {/* Forgot Password (Only for Issuers) */}
            {detectedUserType === USER_TYPES.ISSUER && (
              <TouchableOpacity 
                style={styles.forgotPassword}
                onPress={() => Alert.alert('Reset Password', 'Password reset functionality coming soon!')}
              >
                <Text style={styles.forgotPasswordText}>{t('forgotPassword')}</Text>
              </TouchableOpacity>
            )}

            {/* Sign In Button */}
            <TouchableOpacity
              style={[styles.compactSignInButton, loading && styles.disabledButton]}
              onPress={handleLogin}
              disabled={loading}
            >
              <Text style={styles.signInButtonText}>
                {loading ? 'Signing In...' : t('signInButton')}
              </Text>
            </TouchableOpacity>

            {/* Register Link (Only for Issuers) */}
            {(detectedUserType === USER_TYPES.ISSUER || detectedUserType === USER_TYPES.UNKNOWN) && (
              <View style={styles.compactRegisterContainer}>
                <Text style={styles.registerPrompt}>{t('newToApp')}</Text>
                <TouchableOpacity
                  onPress={() => navigation.navigate('Register')}
                  disabled={loading}
                  style={styles.registerLinkButton}
                >
                  <Text style={styles.registerLink}>{t('createAccountButton')}</Text>
                </TouchableOpacity>
              </View>
            )}

            {/* TEMPORARY: Reset Language Button for Testing */}
            <TouchableOpacity
              onPress={resetLanguagePreference}
              style={styles.resetButton}
            >
              <Text style={styles.resetButtonText}>🔄 Reset Language (Test)</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  subtitleContainer: {
    marginBottom: 20,
    paddingHorizontal: 8,
  },
  subtitle: {
    fontSize: 14, // Smaller compact font
    color: colors.onSurface,
    textAlign: 'center',
    lineHeight: 20, // Compact line height
  },
  formContainer: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16, // More compact padding
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 3,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
    marginBottom: 16,
  },
  userTypeIndicator: {
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: colors.border,
  },
  userTypeText: {
    fontSize: 14,
    fontWeight: '600',
  },
  // Compact Input Styles
  compactInputContainer: {
    marginBottom: 12,
  },
  compactLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text, // Global text color
    marginBottom: 4,
  },
  compactInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 4, // Global 4px border radius
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: 10, // More compact padding
    height: 40, // More compact height
  },
  inputError: {
    borderColor: colors.error,
  },
  inputIcon: {
    marginRight: 8,
  },
  compactTextInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text, // Global text color
    paddingVertical: 0,
  },
  eyeIcon: {
    padding: 4,
  },
  errorText: {
    fontSize: 12,
    color: colors.error,
    marginTop: 2,
    marginLeft: 4,
  },
  hintText: {
    fontSize: 12,
    color: colors.text,
    textAlign: 'center',
    marginBottom: 12,
    fontStyle: 'italic',
    lineHeight: 16,
  },
  forgotPassword: {
    alignSelf: 'flex-start',
    marginBottom: 16,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: colors.primary, // Keep primary color for links
    fontWeight: '500',
  },
  // Compact Button and Link Styles
  compactSignInButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    paddingVertical: 10, // More compact padding
    alignItems: 'center',
    marginBottom: 12, // More compact spacing
  },
  signInButtonText: {
    color: colors.background,
    fontSize: 14, // Smaller compact font
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  compactRegisterContainer: {
    flexDirection: 'column', // Changed to column for next line
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  registerLinkButton: {
    marginTop: 4, // Space between prompt and link
  },
  registerPrompt: {
    fontSize: 12, // Smaller compact font
    color: colors.text, // Global text color
    marginRight: 4,
  },
  registerLink: {
    fontSize: 14,
    color: colors.primary, // Keep primary color for links
    fontWeight: '600',
  },
  // TEMPORARY: Reset button styles
  resetButton: {
    backgroundColor: colors.secondary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 4,
    marginTop: 20,
    alignSelf: 'center',
  },
  resetButtonText: {
    color: colors.background,
    fontSize: 12,
    fontWeight: '500',
  },
});
