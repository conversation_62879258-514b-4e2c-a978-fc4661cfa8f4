import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTranslation } from 'react-i18next';

// Main screens
import HomeScreen from '../screens/main/HomeScreen';
import TransactionsScreen from '../screens/main/TransactionsScreen';
import ReportsScreen from '../screens/main/ReportsScreen';
import SettingsScreen from '../screens/main/SettingsScreen';

// Additional screens that will be added later
import CreateCashBoxScreen from '../screens/main/CreateCashBoxScreen';
import CreateCustodianScreen from '../screens/main/CreateCustodianScreen';
import AddTransactionScreen from '../screens/main/AddTransactionScreen';

// Custodian management screens
import ManageCustodiansScreen from '../screens/main/ManageCustodiansScreen';
import AddCustodianScreen from '../screens/main/AddCustodianScreen';

import { colors } from '../constants/colors';

const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();

function MainTabs() {
  const { t } = useTranslation();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.placeholder,
        tabBarStyle: {
          backgroundColor: colors.background,
          borderTopColor: colors.surface,
        },
        tabBarIcon: ({ focused, color, size }) => {
          let iconName;

          if (route.name === 'Home') {
            iconName = 'home';
          } else if (route.name === 'Transactions') {
            iconName = 'receipt';
          } else if (route.name === 'Reports') {
            iconName = 'bar-chart';
          } else if (route.name === 'Settings') {
            iconName = 'settings';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{ tabBarLabel: t('home', 'Home') }}
      />
      <Tab.Screen
        name="Transactions"
        component={TransactionsScreen}
        options={{ tabBarLabel: t('transactions', 'Transactions') }}
      />
      <Tab.Screen
        name="Reports"
        component={ReportsScreen}
        options={{ tabBarLabel: t('reports', 'Reports') }}
      />
      <Tab.Screen
        name="Settings"
        component={SettingsScreen}
        options={{ tabBarLabel: t('settings', 'Settings') }}
      />
    </Tab.Navigator>
  );
}

export default function MainNavigator() {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen name="CreateCashBox" component={CreateCashBoxScreen} />
      <Stack.Screen name="CreateCustodian" component={CreateCustodianScreen} />
      <Stack.Screen name="AddTransaction" component={AddTransactionScreen} />

      {/* Custodian Management Screens */}
      <Stack.Screen name="ManageCustodians" component={ManageCustodiansScreen} />
      <Stack.Screen name="AddCustodian" component={AddCustodianScreen} />
    </Stack.Navigator>
  );
}

