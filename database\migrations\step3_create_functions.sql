-- =====================================================
-- STEP 3: Create business functions
-- =====================================================
-- Create functions for cash box and transaction management

-- 1. Insert predefined expense categories function
CREATE OR REPLACE FUNCTION create_predefined_categories(cash_box_uuid uuid)
RETURNS void AS $$
BEGIN
  INSERT INTO expense_categories (cash_box_id, name, is_predefined)
  VALUES 
    (cash_box_uuid, 'Office Supplies', true),
    (cash_box_uuid, 'Transportation', true),
    (cash_box_uuid, 'Food & Beverages', true),
    (cash_box_uuid, 'Utilities', true),
    (cash_box_uuid, 'Maintenance', true),
    (cash_box_uuid, 'Communication', true),
    (cash_box_uuid, 'Miscellaneous', true),
    (cash_box_uuid, 'Other', true)
  ON CONFLICT (cash_box_id, name) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create function to create cash box with categories
CREATE OR REPLACE FUNCTION create_cash_box_with_categories(
  issuer_id uuid,
  box_name text,
  box_description text DEFAULT NULL,
  initial_amt decimal DEFAULT 0
)
RETURNS uuid AS $$
DECLARE
  new_cash_box_id uuid;
  user_plan_limit integer;
  current_count integer;
BEGIN
  -- Check user's plan limits
  SELECT sp.cash_box_limit INTO user_plan_limit
  FROM user_roles ur
  JOIN subscription_plans sp ON ur.subscription_plan_id = sp.id
  WHERE ur.user_id = issuer_id;
  
  -- If no plan found, default to free plan limit
  IF user_plan_limit IS NULL THEN
    user_plan_limit := 1;
  END IF;
  
  -- Count current cash boxes
  SELECT COUNT(*) INTO current_count
  FROM cash_boxes
  WHERE issuer_user_id = issuer_id AND is_active = true;
  
  -- Check if user can create more cash boxes
  IF current_count >= user_plan_limit THEN
    RAISE EXCEPTION 'Cash box limit reached. Current plan allows % cash boxes.', user_plan_limit;
  END IF;
  
  -- Create cash box
  INSERT INTO cash_boxes (issuer_user_id, name, description, initial_amount, current_balance)
  VALUES (issuer_id, box_name, box_description, initial_amt, initial_amt)
  RETURNING id INTO new_cash_box_id;
  
  -- Create predefined categories
  PERFORM create_predefined_categories(new_cash_box_id);
  
  RETURN new_cash_box_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create function to record transaction
CREATE OR REPLACE FUNCTION create_transaction(
  p_cash_box_id uuid,
  p_custodian_user_id uuid,
  p_expense_category_id uuid,
  p_amount decimal,
  p_description text DEFAULT NULL,
  p_image_url text DEFAULT NULL,
  p_image_filename text DEFAULT NULL,
  p_voice_memo_url text DEFAULT NULL,
  p_voice_memo_filename text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
  new_transaction_id uuid;
  current_balance decimal;
BEGIN
  -- Get current balance
  SELECT current_balance INTO current_balance
  FROM cash_boxes
  WHERE id = p_cash_box_id;
  
  -- Create transaction
  INSERT INTO transactions (
    cash_box_id, custodian_user_id, expense_category_id, amount, description,
    image_url, image_filename, voice_memo_url, voice_memo_filename
  )
  VALUES (
    p_cash_box_id, p_custodian_user_id, p_expense_category_id, p_amount, p_description,
    p_image_url, p_image_filename, p_voice_memo_url, p_voice_memo_filename
  )
  RETURNING id INTO new_transaction_id;
  
  -- Update cash box balance
  UPDATE cash_boxes
  SET current_balance = current_balance - p_amount,
      updated_at = now()
  WHERE id = p_cash_box_id;
  
  RETURN new_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
