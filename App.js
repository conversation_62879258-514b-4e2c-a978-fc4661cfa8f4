import 'react-native-gesture-handler';
import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { View, ActivityIndicator } from 'react-native';
import './src/i18n/i18n';
import { initializeLanguage, hasLanguagePreference } from './src/services/languageService';
import { colors } from './src/constants/colors';

import AuthNavigator from './src/navigation/AuthNavigator';
import MainNavigator from './src/navigation/MainNavigator';
import LanguageSelectionScreen from './src/screens/auth/LanguageSelectionScreen';
import { AuthProvider, useAuth } from './src/context/AuthContext';

const Stack = createStackNavigator();

function AppNavigator() {
  const { isAuthenticated } = useAuth();
  const [hasLanguagePref, setHasLanguagePref] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    checkLanguagePreference();
  }, []);

  const checkLanguagePreference = async () => {
    try {
      const hasPreference = await hasLanguagePreference();
      setHasLanguagePref(hasPreference);

      if (hasPreference) {
        // Initialize language if preference exists
        await initializeLanguage();
      }
    } catch (error) {
      console.error('Error checking language preference:', error);
      setHasLanguagePref(false);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: colors.background }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <StatusBar style="dark" />
      {isAuthenticated ? (
        <MainNavigator />
      ) : hasLanguagePref ? (
        <AuthNavigator />
      ) : (
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          <Stack.Screen name="LanguageSelection" component={LanguageSelectionScreen} />
          <Stack.Screen name="Auth" component={AuthNavigator} />
        </Stack.Navigator>
      )}
    </NavigationContainer>
  );
}

export default function App() {
  useEffect(() => {
    // Initialize language on app start
    initializeLanguage();
  }, []);

  return (
    <AuthProvider>
      <AppNavigator />
    </AuthProvider>
  );
}
