# 🧾 Petty Cash Transaction Recorder App – Product Requirements Document (PRD)

## App Nme
Currently use 'khrpani'.  I should be able to change later 

## Overview
A freemium mobile app to record and manage petty cash transactions, especially for workers or project site custodians. It allows capturing transactions, tracking balances, setting up cash boxes, and managing replenishment cycles with multilingual and voice memo support.

---

## Core Entities

### 1. Issuer (Profile)
- Person/Company issuing the petty cash.
- Can access all reports and settings.
- Login is required.
- **Login Persistence**: Once logged in, app keeps the user signed in unless manually logged out.

### 2. Cash Box
- Represents a location/project or purpose (e.g., “Site A”, “Event B”).
- Tracks all related transactions.
- **Multiple Cash Boxes** available in **paid version**.

### 3. <PERSON><PERSON><PERSON><PERSON> (Profile)
- Person handling petty cash on behalf of Issuer.
- Assigned to one or more Cash Boxes.
- Can record transactions.
- **Multiple Custodians** available in **paid version**.

### 4. Transaction
- Amount spent with:
  - Expense Category
  - Optional Description (Paid only)
  - **Voice Memo** (Paid only): Add short audio recording describing expense
  - Amount
  - Bill photo (Paid only)
- Linked to Cash Box and Custodian
- Timestamped automatically

### 5. Replenishment Cycle
- Set once per Cash Box:
  - One-time
  - Weekly
  - Monthly
  - Threshold based (when amount drops below x)

---

## Features

### ✅ Free Version
- 1 Cash Box
- 1 Custodian
- Prebuilt expense items list (dropdown + Other)
- Weekly Report for Issuer
- 3-month data history
- **Persistent Login**
- **App available in multiple languages (Hindi, Marathi, Bengali)**

### 🔓 Paid Version
- Multiple Cash Boxes and Custodians
- Customizable Expense List (per Cash Box)
- Bill photo upload
- Voice Memo recording
- Description field for each entry
- Clear Cash Box/Custodian data separately
- Reports:
  - Daily, Weekly, Monthly, FY
  - Filter by Cash Box, Expense Type
  - Graphical Dashboard
- 12-month Data Backup

---

## Settings

- **Select App Language** (User setting)
- Add/Edit/Delete Cash Box (Paid)
- Add/Edit/Delete Custodian (Paid)
- Manage Expense Categories per Cash Box (Paid)
- Clear All / Per Section Data (Paid)
- Grant access to Custodian for report view (Paid)

---

## Color Scheme
- **Please use the below Color Scheme**
  
- primary: '#D10000', // Bright Red
- accent: '#f59e0b',  // Yellow-Orange
- secondary: '#181818', // Soft Black
- danger: '#D10000', // Bright Red
- success: '#009900', // Green
- background: '#ffffff', // White
- surface: '#f8f9fb', // light gray
- text: '#242424', // Soft black
- onSurface: '#363636', // Slightly lighter black
- placeholder: '#818181',
- error: '#D10000', // Bright Red
- green: '#009900', // Green
- blue: '#003399', // Blue
- yellow: '#f59e0b',  // Yellow

  ---

## Data Privacy
- Data is stored locally or synced with cloud (future version)
- Access control through secure login

---

## Future Enhancements (Post-MVP)
- Cloud sync for issuer backup
- Export reports to PDF/Excel
- Notifications for low balance / weekly spend