/**
 * Smart Login Detection Utilities
 * Auto-detects whether user is entering email (Issuer) or phone (Custodian)
 */

export const USER_TYPES = {
  ISSUER: 'issuer',
  CUSTODIAN: 'custodian',
  UNKNOWN: 'unknown'
};

/**
 * Detect user type based on input
 * @param {string} input - User input (email or phone)
 * @returns {string} - USER_TYPES.ISSUER, USER_TYPES.CUSTODIAN, or USER_TYPES.UNKNOWN
 */
export const detectUserType = (input) => {
  if (!input || input.trim().length === 0) {
    return USER_TYPES.UNKNOWN;
  }

  const trimmedInput = input.trim();

  // Email detection (contains @ symbol and basic email format)
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (emailRegex.test(trimmedInput)) {
    return USER_TYPES.ISSUER;
  }

  // Phone detection (numbers, spaces, dashes, parentheses, plus sign)
  const phoneRegex = /^[\+]?[\d\s\-\(\)]{7,15}$/;
  if (phoneRegex.test(trimmedInput)) {
    return USER_TYPES.CUSTODIAN;
  }

  // Partial email detection (has @ but not complete)
  if (trimmedInput.includes('@')) {
    return USER_TYPES.ISSUER;
  }

  // Partial phone detection (only numbers and basic phone chars)
  const partialPhoneRegex = /^[\+\d\s\-\(\)]+$/;
  if (partialPhoneRegex.test(trimmedInput) && trimmedInput.length >= 3) {
    return USER_TYPES.CUSTODIAN;
  }

  return USER_TYPES.UNKNOWN;
};

/**
 * Get placeholder text based on detected user type
 * @param {string} userType - Detected user type
 * @param {function} t - Translation function (optional)
 * @returns {string} - Appropriate placeholder text
 */
export const getIdentifierPlaceholder = (userType, t = null) => {
  if (!t) {
    // Fallback to English if no translation function provided
    switch (userType) {
      case USER_TYPES.ISSUER:
        return 'Enter your email address';
      case USER_TYPES.CUSTODIAN:
        return 'Enter your phone number';
      default:
        return 'Email address or phone number';
    }
  }

  switch (userType) {
    case USER_TYPES.ISSUER:
      return t('enterEmail', 'Enter your email address');
    case USER_TYPES.CUSTODIAN:
      return t('enterPhoneNumber', 'Enter your phone number');
    default:
      return t('emailAddressOrPhoneNumber', 'Email address or phone number');
  }
};

/**
 * Get credential placeholder text based on detected user type
 * @param {string} userType - Detected user type
 * @param {function} t - Translation function (optional)
 * @returns {string} - Appropriate placeholder text
 */
export const getCredentialPlaceholder = (userType, t = null) => {
  if (!t) {
    // Fallback to English if no translation function provided
    switch (userType) {
      case USER_TYPES.ISSUER:
        return 'Enter your password';
      case USER_TYPES.CUSTODIAN:
        return 'Enter your PIN';
      default:
        return 'Password or PIN';
    }
  }

  switch (userType) {
    case USER_TYPES.ISSUER:
      return t('enterPassword', 'Enter your password');
    case USER_TYPES.CUSTODIAN:
      return t('enterPin', 'Enter your PIN');
    default:
      return t('passwordOrPin', 'Password or PIN');
  }
};

/**
 * Get keyboard type based on detected user type
 * @param {string} userType - Detected user type
 * @param {string} field - 'identifier' or 'credential'
 * @returns {string} - React Native keyboard type
 */
export const getKeyboardType = (userType, field) => {
  if (field === 'identifier') {
    switch (userType) {
      case USER_TYPES.ISSUER:
        return 'email-address';
      case USER_TYPES.CUSTODIAN:
        return 'phone-pad';
      default:
        return 'default';
    }
  } else if (field === 'credential') {
    switch (userType) {
      case USER_TYPES.ISSUER:
        return 'default';
      case USER_TYPES.CUSTODIAN:
        return 'numeric';
      default:
        return 'default';
    }
  }
  return 'default';
};

/**
 * Get max length for credential field
 * @param {string} userType - Detected user type
 * @returns {number|undefined} - Max length or undefined for no limit
 */
export const getCredentialMaxLength = (userType) => {
  switch (userType) {
    case USER_TYPES.CUSTODIAN:
      return 6; // PIN is typically 4-6 digits
    default:
      return undefined; // No limit for passwords
  }
};

/**
 * Get hint text based on detected user type
 * @param {string} userType - Detected user type
 * @param {function} t - Translation function (optional)
 * @returns {string} - Helpful hint text
 */
export const getHintText = (userType, t = null) => {
  if (!t) {
    // Fallback to English if no translation function provided
    switch (userType) {
      case USER_TYPES.ISSUER:
        return 'Sign in with your email and password to manage your account';
      case USER_TYPES.CUSTODIAN:
        return 'Enter the phone number and PIN provided by your issuer';
      default:
        return 'Enter your email (for account owners) or phone number (for custodians)';
    }
  }

  switch (userType) {
    case USER_TYPES.ISSUER:
      return t('issuerHint', 'Sign in with your email and password to manage your account');
    case USER_TYPES.CUSTODIAN:
      return t('custodianHint', 'Enter the phone number and PIN provided by your issuer');
    default:
      return t('loginHint', 'Enter your email (for account owners) or phone number (for custodians)');
  }
};

/**
 * Validate input based on user type
 * @param {string} identifier - Email or phone
 * @param {string} credential - Password or PIN
 * @param {string} userType - Detected user type
 * @returns {object} - Validation result with isValid and errors
 */
export const validateLoginInput = (identifier, credential, userType) => {
  const errors = {};
  let isValid = true;

  // Validate identifier
  if (!identifier || identifier.trim().length === 0) {
    errors.identifier = 'This field is required';
    isValid = false;
  } else if (userType === USER_TYPES.ISSUER) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(identifier.trim())) {
      errors.identifier = 'Please enter a valid email address';
      isValid = false;
    }
  } else if (userType === USER_TYPES.CUSTODIAN) {
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{7,15}$/;
    if (!phoneRegex.test(identifier.trim())) {
      errors.identifier = 'Please enter a valid phone number';
      isValid = false;
    }
  }

  // Validate credential
  if (!credential || credential.trim().length === 0) {
    errors.credential = 'This field is required';
    isValid = false;
  } else if (userType === USER_TYPES.ISSUER) {
    if (credential.length < 6) {
      errors.credential = 'Password must be at least 6 characters';
      isValid = false;
    }
  } else if (userType === USER_TYPES.CUSTODIAN) {
    if (!/^\d{4,6}$/.test(credential)) {
      errors.credential = 'PIN must be 4-6 digits';
      isValid = false;
    }
  }

  return { isValid, errors };
};

export default {
  USER_TYPES,
  detectUserType,
  getIdentifierPlaceholder,
  getCredentialPlaceholder,
  getKeyboardType,
  getCredentialMaxLength,
  getHintText,
  validateLoginInput
};
