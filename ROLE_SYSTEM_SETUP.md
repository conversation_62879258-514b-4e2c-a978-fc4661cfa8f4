# 🔐 Role System Setup Guide

## Overview
This guide will help you set up the complete role-based access control system for KharchaPani. The system is designed to be **safe for production** and **future-proof**.

## 🚀 Quick Setup (5 minutes)

### Step 1: Run Database Migration
1. **Open your Supabase Dashboard**
2. **Go to SQL Editor**
3. **Copy and paste** the entire content from `database/migrations/001_create_role_system.sql`
4. **Click "Run"**

### Step 2: Create Your Superadmin Account
1. **Sign up normally** in your app (this creates a free_user)
2. **In Supabase SQL Editor**, run this command with YOUR user ID:

```sql
-- Replace 'YOUR_USER_ID_HERE' with your actual user ID from auth.users table
UPDATE user_roles 
SET role = 'superadmin' 
WHERE user_id = 'YOUR_USER_ID_HERE';
```

**To find your user ID:**
```sql
-- Run this to see all users and their IDs
SELECT id, email, created_at FROM auth.users ORDER BY created_at DESC;
```

### Step 3: Test the System
1. **Restart your app**
2. **Login with your account**
3. **Check console logs** - you should see your role as 'superadmin'

## ✅ What You Get

### 🔒 **Automatic Security**
- ✅ **Row Level Security (RLS)** enabled
- ✅ **Users can only see their own data**
- ✅ **Admins can see aggregated data only**
- ✅ **Superadmins have full access**

### 👥 **Role Hierarchy**
```
SUPERADMIN (You)
├── Full access to everything
├── Can create/modify admin accounts
├── Can view all user data
└── Cannot be created via signup

ADMIN (Future team members)
├── Can view analytics (anonymized)
├── Can handle customer support
├── Cannot access individual user data
└── Created only by SUPERADMIN

PREMIUM_USER
├── Access to premium features
├── Voice memos, bill photos
├── Advanced reports, export data
└── Own data only

FREE_USER (Default)
├── Basic transaction recording
├── Basic reports
├── Limited features
└── Own data only
```

### 🛡️ **Built-in Components**
```javascript
// Use these in your app immediately:

// Show content only for premium users
<PremiumOnly fallback={<UpgradePrompt />}>
  <VoiceMemoFeature />
</PremiumOnly>

// Show content only for admins
<AdminOnly>
  <UserAnalytics />
</AdminOnly>

// Show content only for superadmin
<SuperAdminOnly>
  <RoleManagement />
</SuperAdminOnly>

// Feature-based access control
<FeatureGuard feature="voice_memos">
  <VoiceMemoButton />
</FeatureGuard>
```

### 📊 **Role Checking in Code**
```javascript
// In any component
const { userRole, hasFeatureAccess, hasRole } = useAuth();

// Check specific role
if (hasRole('superadmin')) {
  // Show superadmin features
}

// Check feature access
if (hasFeatureAccess('voice_memos')) {
  // Show voice memo button
}

// Check current role
console.log('Current role:', userRole); // 'superadmin', 'admin', 'premium_user', 'free_user'
```

## 🔧 **Adding Admins Later (When Needed)**

### Option 1: Via SQL (Recommended)
```sql
-- Find the user you want to make admin
SELECT id, email FROM auth.users WHERE email = '<EMAIL>';

-- Update their role
UPDATE user_roles 
SET role = 'admin' 
WHERE user_id = 'USER_ID_FROM_ABOVE';
```

### Option 2: Via App (Future Feature)
```javascript
// You can build an admin panel later with this function
import { updateUserRole } from '../services/roleService';

const makeUserAdmin = async (userId) => {
  const result = await updateUserRole(userId, 'admin');
  if (result.success) {
    console.log('User is now an admin!');
  }
};
```

## 🚨 **Security Features**

### **Data Isolation**
- ✅ **RLS policies** prevent users from seeing each other's data
- ✅ **Database-level enforcement** (even if frontend is compromised)
- ✅ **Automatic filtering** by user_id

### **Role Protection**
- ✅ **Only superadmins** can modify roles
- ✅ **Admins cannot escalate** their own permissions
- ✅ **Users cannot see** other users' roles

### **Feature Gating**
- ✅ **Premium features** blocked for free users
- ✅ **Admin features** blocked for regular users
- ✅ **Graceful fallbacks** with upgrade prompts

## 📈 **Scaling Strategy**

### **Phase 1 (Launch - 100 users)**
- ✅ You as SUPERADMIN only
- ✅ Monitor everything yourself
- ✅ Focus on user experience

### **Phase 2 (100+ users)**
- ✅ Add 1 trusted ADMIN for customer support
- ✅ Use built-in analytics dashboard
- ✅ Automated role management

### **Phase 3 (1000+ users)**
- ✅ Multiple specialized admin roles
- ✅ Advanced analytics
- ✅ Automated billing/subscription management

## 🧪 **Testing Your Setup**

### Test 1: Role Assignment
```javascript
// In your app, check the console
const { userRole } = useAuth();
console.log('My role:', userRole); // Should be 'superadmin'
```

### Test 2: Feature Access
```javascript
// Test premium feature access
const { hasFeatureAccess } = useAuth();
console.log('Can use voice memos:', hasFeatureAccess('voice_memos')); // Should be true
```

### Test 3: Component Guards
```javascript
// This should show content for you (superadmin)
<SuperAdminOnly>
  <Text>You are a superadmin!</Text>
</SuperAdminOnly>
```

## 🆘 **Troubleshooting**

### Issue: "Role is null"
**Solution:** Make sure you ran the database migration and your user has a role assigned.

### Issue: "Permission denied"
**Solution:** Check that RLS policies are enabled and your user_id matches the role assignment.

### Issue: "Cannot update roles"
**Solution:** Only superadmins can update roles. Make sure you're logged in as superadmin.

## 🎯 **Next Steps**

1. ✅ **Run the migration** (5 minutes)
2. ✅ **Make yourself superadmin** (2 minutes)
3. ✅ **Test the system** (5 minutes)
4. ✅ **Start using role guards** in your app
5. ✅ **Build your freemium features** with confidence

**Your app is now enterprise-ready with bulletproof security!** 🛡️
