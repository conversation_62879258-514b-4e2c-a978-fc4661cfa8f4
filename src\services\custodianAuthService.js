import { supabase } from '../lib/supabase';

/**
 * Custodian Authentication Service
 * Handles phone + PIN authentication for custodians
 */

/**
 * Authenticate custodian using phone and PIN
 * @param {string} phone - Phone number
 * @param {string} pin - PIN code
 * @returns {Promise<{success: boolean, data: object|null, error: string|null}>}
 */
export const authenticateCustodian = async (phone, pin) => {
  try {
    // Clean phone number (remove spaces, dashes, etc.)
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    // Call the RPC function to authenticate custodian
    const { data, error } = await supabase.rpc('authenticate_custodian', {
      phone: cleanPhone,
      pin: pin
    });

    if (error) {
      console.error('Custodian authentication error:', error);
      return { 
        success: false, 
        data: null, 
        error: 'Invalid phone number or PIN' 
      };
    }

    if (!data || data.length === 0) {
      return { 
        success: false, 
        data: null, 
        error: 'Invalid phone number or PIN' 
      };
    }

    const custodianData = data[0];
    
    return {
      success: true,
      data: {
        custodianUserId: custodianData.user_id,
        issuerUserId: custodianData.issuer_id,
        fullName: custodianData.full_name,
        phone: cleanPhone
      },
      error: null
    };

  } catch (error) {
    console.error('Error in authenticateCustodian:', error);
    return { 
      success: false, 
      data: null, 
      error: 'Authentication failed. Please try again.' 
    };
  }
};

/**
 * Create a temporary session for custodian
 * Since custodians don't have Supabase auth accounts, we create a custom session
 * @param {object} custodianData - Custodian authentication data
 * @returns {Promise<{success: boolean, session: object|null, error: string|null}>}
 */
export const createCustodianSession = async (custodianData) => {
  try {
    // For now, we'll store custodian session in AsyncStorage
    // In a production app, you might want to use a more secure approach
    const sessionData = {
      userType: 'custodian',
      custodianUserId: custodianData.custodianUserId,
      issuerUserId: custodianData.issuerUserId,
      fullName: custodianData.fullName,
      phone: custodianData.phone,
      loginTime: new Date().toISOString(),
      // Add a simple session token (in production, use proper JWT)
      sessionToken: `custodian_${custodianData.custodianUserId}_${Date.now()}`
    };

    return {
      success: true,
      session: sessionData,
      error: null
    };

  } catch (error) {
    console.error('Error creating custodian session:', error);
    return {
      success: false,
      session: null,
      error: 'Failed to create session'
    };
  }
};

/**
 * Validate custodian session
 * @param {object} session - Custodian session data
 * @returns {boolean} - Whether session is valid
 */
export const validateCustodianSession = (session) => {
  if (!session || session.userType !== 'custodian') {
    return false;
  }

  // Check if session is not too old (24 hours)
  const loginTime = new Date(session.loginTime);
  const now = new Date();
  const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
  
  if (hoursDiff > 24) {
    return false; // Session expired
  }

  return true;
};

/**
 * Get custodian details by user ID
 * @param {string} custodianUserId - Custodian user ID
 * @returns {Promise<{success: boolean, data: object|null, error: string|null}>}
 */
export const getCustodianDetails = async (custodianUserId) => {
  try {
    const { data, error } = await supabase
      .from('custodians')
      .select(`
        id,
        phone_number,
        full_name,
        is_active,
        created_at,
        issuer_user_id
      `)
      .eq('custodian_user_id', custodianUserId)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching custodian details:', error);
      return {
        success: false,
        data: null,
        error: 'Failed to fetch custodian details'
      };
    }

    return {
      success: true,
      data: data,
      error: null
    };

  } catch (error) {
    console.error('Error in getCustodianDetails:', error);
    return {
      success: false,
      data: null,
      error: 'Failed to fetch custodian details'
    };
  }
};

/**
 * Check if phone number belongs to an active custodian
 * @param {string} phone - Phone number to check
 * @returns {Promise<{exists: boolean, error: string|null}>}
 */
export const checkCustodianExists = async (phone) => {
  try {
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    
    const { data, error } = await supabase
      .from('custodians')
      .select('id')
      .eq('phone_number', cleanPhone)
      .eq('is_active', true)
      .limit(1);

    if (error) {
      console.error('Error checking custodian existence:', error);
      return {
        exists: false,
        error: 'Failed to verify phone number'
      };
    }

    return {
      exists: data && data.length > 0,
      error: null
    };

  } catch (error) {
    console.error('Error in checkCustodianExists:', error);
    return {
      exists: false,
      error: 'Failed to verify phone number'
    };
  }
};

export default {
  authenticateCustodian,
  createCustodianSession,
  validateCustodianSession,
  getCustodianDetails,
  checkCustodianExists
};
