import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../constants/colors';

export default function AppHeader({ 
  showNotification = true, 
  showAvatar = true, 
  onNotificationPress, 
  onAvatarPress 
}) {
  return (
    <>
      <StatusBar 
        barStyle="dark-content" 
        backgroundColor={colors.background} 
        translucent={false}
      />
      <View style={styles.header}>
        {/* Left Side - Logo and App Name */}
        <View style={styles.leftSection}>
          <Image 
            source={require('../../assets/khrpani-logo.png')} 
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.appName}>KharchaPani</Text>
        </View>

        {/* Right Side - Notification and Avatar */}
        <View style={styles.rightSection}>
          {showNotification && (
            <TouchableOpacity 
              style={styles.iconButton}
              onPress={onNotificationPress}
            >
              <Icon name="notifications" size={24} color={colors.text} />
            </TouchableOpacity>
          )}
          
          {showAvatar && (
            <TouchableOpacity 
              style={styles.avatarButton}
              onPress={onAvatarPress}
            >
              <View style={styles.avatar}>
                <Icon name="person" size={20} color={colors.background} />
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.surface,
    // Add safe area padding for different devices
    paddingTop: Platform.OS === 'ios' ? 44 : 12,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  logo: {
    width: 32,
    height: 32,
    marginRight: 8,
  },
  appName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: 8,
    marginRight: 8,
  },
  avatarButton: {
    padding: 4,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
