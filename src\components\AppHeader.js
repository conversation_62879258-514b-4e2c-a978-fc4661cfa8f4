import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useTranslation } from 'react-i18next';
import { colors } from '../constants/colors';
import AvatarDropdown from './AvatarDropdown';

export default function AppHeader({
  showNotification = true,
  showAvatar = true,
  onNotificationPress
}) {
  const { t } = useTranslation();
  return (
    <>
      <StatusBar 
        barStyle="dark-content" 
        backgroundColor={colors.background} 
        translucent={false}
      />
      <View style={styles.header}>
        {/* Left Side - Logo and App Name */}
        <View style={styles.leftSection}>
          <Image
            source={require('../../assets/khrpani-logo.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <Text style={styles.appName}>{t('appName')}</Text>
        </View>

        {/* Right Side - Notification and Avatar */}
        <View style={styles.rightSection}>
          {showNotification && (
            <TouchableOpacity 
              style={styles.iconButton}
              onPress={onNotificationPress}
            >
              <Icon name="notifications" size={24} color={colors.text} />
            </TouchableOpacity>
          )}
          
          {showAvatar && (
            <AvatarDropdown />
          )}
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    // Add safe area padding for mobile status bar (time, signal, battery)
    paddingTop: Platform.OS === 'ios' ? 50 : 35,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1, // Takes available space, pushes right section to the right
  },
  logo: {
    width: 45,
    height: 45,
    marginRight: 12, // More space between logo and text
  },
  appName: {
    fontSize: 21, // Normal font (igonore global guidelines)
    fontWeight: 'bold',
    color: colors.secondary, // Use secondary color for brand name
    flex: 1, // Takes remaining space in left section
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    padding: 8,
    marginRight: 8,
  },
});
