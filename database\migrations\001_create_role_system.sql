-- =====================================================
-- KharchaPani Role System & Security Setup
-- =====================================================
-- This creates a complete role-based access control system
-- Safe to run multiple times (uses IF NOT EXISTS)

-- 1. Create user_roles table
CREATE TABLE IF NOT EXISTS user_roles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  role text NOT NULL CHECK (role IN ('free_user', 'premium_user', 'admin', 'superadmin')),
  permissions jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid REFERENCES auth.users(id),
  
  -- Ensure one role per user
  UNIQUE(user_id)
);

-- 2. Create function to automatically assign default role to new users
CREATE OR REPLACE FUNCTION assign_default_role()
RETURNS TRIGGER AS $$
BEGIN
  -- Assign 'free_user' role to all new signups
  INSERT INTO user_roles (user_id, role)
  VALUES (NEW.id, 'free_user')
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create trigger to auto-assign role on user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION assign_default_role();

-- 4. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 5. Create trigger for updated_at
DROP TRIGGER IF EXISTS update_user_roles_updated_at ON user_roles;
CREATE TRIGGER update_user_roles_updated_at
  BEFORE UPDATE ON user_roles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 6. Create helper function to check user role
CREATE OR REPLACE FUNCTION get_user_role(user_uuid uuid)
RETURNS text AS $$
DECLARE
  user_role text;
BEGIN
  SELECT role INTO user_role
  FROM user_roles
  WHERE user_id = user_uuid;
  
  RETURN COALESCE(user_role, 'free_user');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Create helper function to check if user has permission
CREATE OR REPLACE FUNCTION user_has_role(user_uuid uuid, required_roles text[])
RETURNS boolean AS $$
DECLARE
  user_role text;
BEGIN
  SELECT role INTO user_role
  FROM user_roles
  WHERE user_id = user_uuid;
  
  RETURN user_role = ANY(required_roles);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Enable Row Level Security on user_roles table
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies for user_roles table
-- Users can see their own role
CREATE POLICY "users_can_see_own_role" ON user_roles
  FOR SELECT
  USING (auth.uid() = user_id);

-- Admins and superadmins can see all roles
CREATE POLICY "admins_can_see_all_roles" ON user_roles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      WHERE ur.user_id = auth.uid()
      AND ur.role IN ('admin', 'superadmin')
    )
  );

-- Only superadmins can modify roles
CREATE POLICY "only_superadmin_can_modify_roles" ON user_roles
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM user_roles ur
      WHERE ur.user_id = auth.uid()
      AND ur.role = 'superadmin'
    )
  );

-- 10. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role ON user_roles(role);

-- 11. Insert comments for documentation
COMMENT ON TABLE user_roles IS 'Stores user roles and permissions for the application';
COMMENT ON COLUMN user_roles.role IS 'User role: free_user, premium_user, admin, superadmin';
COMMENT ON COLUMN user_roles.permissions IS 'Additional JSON permissions for fine-grained control';
COMMENT ON FUNCTION assign_default_role() IS 'Automatically assigns free_user role to new signups';
COMMENT ON FUNCTION get_user_role(uuid) IS 'Helper function to get user role by user_id';
COMMENT ON FUNCTION user_has_role(uuid, text[]) IS 'Helper function to check if user has required role';
