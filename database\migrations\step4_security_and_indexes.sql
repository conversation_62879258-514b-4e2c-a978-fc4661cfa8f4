-- =====================================================
-- STEP 4: Add security (RLS) and performance indexes
-- =====================================================
-- Enable RLS and create policies and indexes

-- 1. Enable RLS on all tables
ALTER TABLE cash_boxes ENABLE ROW LEVEL SECURITY;
ALTER TABLE expense_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE cash_box_custodians ENABLE ROW LEVEL SECURITY;

-- 2. Create RLS policies (drop existing first to avoid conflicts)

-- Cash boxes: Users can only see their own cash boxes
DROP POLICY IF EXISTS "users_can_manage_own_cash_boxes" ON cash_boxes;
CREATE POLICY "users_can_manage_own_cash_boxes" ON cash_boxes
  FOR ALL
  USING (auth.uid() = issuer_user_id);

-- Expense categories: Users can see categories for their cash boxes
DROP POLICY IF EXISTS "users_can_manage_expense_categories" ON expense_categories;
CREATE POLICY "users_can_manage_expense_categories" ON expense_categories
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = expense_categories.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
  );

-- Transactions: Users can see transactions for their cash boxes OR transactions they created as custodians
DROP POLICY IF EXISTS "users_can_view_relevant_transactions" ON transactions;
CREATE POLICY "users_can_view_relevant_transactions" ON transactions
  FOR SELECT
  USING (
    -- Issuer can see all transactions in their cash boxes
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = transactions.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
    OR
    -- Custodians can see their own transactions
    auth.uid() = custodian_user_id
  );

DROP POLICY IF EXISTS "custodians_can_create_transactions" ON transactions;
CREATE POLICY "custodians_can_create_transactions" ON transactions
  FOR INSERT
  WITH CHECK (auth.uid() = custodian_user_id);

DROP POLICY IF EXISTS "issuers_can_manage_transactions" ON transactions;
CREATE POLICY "issuers_can_manage_transactions" ON transactions
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = transactions.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
  );

-- Cash box custodians: Issuers can manage assignments
DROP POLICY IF EXISTS "issuers_can_manage_assignments" ON cash_box_custodians;
CREATE POLICY "issuers_can_manage_assignments" ON cash_box_custodians
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM cash_boxes cb
      WHERE cb.id = cash_box_custodians.cash_box_id
      AND cb.issuer_user_id = auth.uid()
    )
  );

-- 3. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_cash_boxes_issuer ON cash_boxes(issuer_user_id);
CREATE INDEX IF NOT EXISTS idx_cash_boxes_active ON cash_boxes(is_active);
CREATE INDEX IF NOT EXISTS idx_expense_categories_cash_box ON expense_categories(cash_box_id);
CREATE INDEX IF NOT EXISTS idx_transactions_cash_box ON transactions(cash_box_id);
CREATE INDEX IF NOT EXISTS idx_transactions_custodian ON transactions(custodian_user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date);
CREATE INDEX IF NOT EXISTS idx_cash_box_custodians_box ON cash_box_custodians(cash_box_id);
CREATE INDEX IF NOT EXISTS idx_cash_box_custodians_custodian ON cash_box_custodians(custodian_id);
