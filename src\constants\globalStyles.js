import { StyleSheet } from 'react-native';
import { colors } from './colors';

export const globalStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  safeArea: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  // GLOBAL RULE: Last element margin bottom
  screenContainer: {
    flex: 1,
    backgroundColor: colors.background,
    paddingBottom: 27, // Global 27px bottom margin for mobile overlay buttons
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  compactSpacing: {
    marginVertical: 6, // Compact spacing between elements
  },
  // GLOBAL RULE: 4px border radius for all elements
  card: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 12, // Compact padding
    marginVertical: 6, // Compact spacing
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 3,
  },
  compactCard: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 10, // More compact padding
    marginVertical: 4, // Compact spacing
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.06,
    shadowRadius: 1,
    elevation: 2,
  },
  // GLOBAL RULE: Compact buttons
  button: {
    backgroundColor: colors.primary,
    paddingVertical: 10, // Compact padding
    paddingHorizontal: 20, // Compact padding
    borderRadius: 4, // Global 4px border radius
    alignItems: 'center',
  },
  compactButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8, // More compact
    paddingHorizontal: 16, // More compact
    borderRadius: 4, // Global 4px border radius
    alignItems: 'center',
  },
  buttonText: {
    color: colors.background,
    fontSize: 14, // Smaller compact font
    fontWeight: 'bold',
  },
  compactButtonText: {
    color: colors.background,
    fontSize: 13, // Smaller compact font
    fontWeight: 'bold',
  },
  // GLOBAL RULE: Compact inputs with 4px border radius
  input: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4, // Global 4px border radius
    paddingHorizontal: 10, // Compact padding
    paddingVertical: 8, // Compact padding
    fontSize: 14, // Smaller compact font
    backgroundColor: colors.background,
  },
  compactInput: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 4, // Global 4px border radius
    paddingHorizontal: 8, // More compact padding
    paddingVertical: 6, // More compact padding
    fontSize: 13, // Smaller compact font
    backgroundColor: colors.background,
    marginVertical: 3, // Compact spacing
  },
  // GLOBAL RULE: Text colors - colors.text for ALL text
  label: {
    fontSize: 14, // Smaller compact font
    fontWeight: '600',
    color: colors.text, // Global text color
    marginBottom: 6, // Compact spacing
  },
  compactLabel: {
    fontSize: 12, // Smaller compact font
    fontWeight: '600',
    color: colors.text, // Global text color
    marginBottom: 3, // Compact spacing
  },
  // GLOBAL RULE: Heading colors - colors.onSurface for ALL headings
  title: {
    fontSize: 20, // Smaller compact font
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
  },
  compactTitle: {
    fontSize: 18, // Smaller compact font
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14, // Smaller compact font
    color: colors.text, // Global text color
    textAlign: 'center',
    marginTop: 6, // Compact spacing
  },
  compactSubtitle: {
    fontSize: 12, // Smaller compact font
    color: colors.text, // Global text color
    textAlign: 'center',
    marginTop: 4, // Compact spacing
  },
});
