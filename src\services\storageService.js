import { supabase } from '../lib/supabase';
import * as FileSystem from 'expo-file-system';
import ImageCompressionService from './imageCompressionService';
import UsageTrackingService from './usageTrackingService';

/**
 * Storage Service
 * Handles file uploads to Supabase Storage with compression and usage tracking
 * 
 * Features:
 * - Automatic image compression before upload
 * - Usage limit enforcement
 * - Progress tracking
 * - Error handling and retry logic
 */

class StorageService {
  
  // Storage bucket configuration
  static BUCKETS = {
    TRANSACTION_IMAGES: 'transaction-images',
    VOICE_MEMOS: 'voice-memos',
    PROFILE_PICTURES: 'profile-pictures',
  };

  // File naming conventions
  static generateFileName(userId, type, extension) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const random = Math.random().toString(36).substring(2, 8);
    return `${userId}/${type}/${timestamp}-${random}.${extension}`;
  }

  /**
   * Upload compressed image to Supabase Storage
   * @param {string} imageUri - Local image URI
   * @param {string} userId - User ID
   * @param {Object} options - Upload options
   * @returns {Promise<Object>} Upload result with public URL
   */
  static async uploadTransactionImage(imageUri, userId, options = {}) {
    try {
      console.log('🚀 Starting transaction image upload...');

      // Check usage limits first
      const usageCheck = await UsageTrackingService.checkImageUploadLimit(userId);
      if (!usageCheck.canUpload) {
        throw new Error(`Daily image limit reached (${usageCheck.limit}). Resets in ${UsageTrackingService.getTimeUntilReset()}`);
      }

      console.log(`📊 Usage check passed: ${usageCheck.currentCount}/${usageCheck.limit} images used today`);

      // Compress image
      const compressedImage = await ImageCompressionService.compressImage(imageUri);
      
      // Validate compressed image
      const validation = ImageCompressionService.validateCompressedImage(compressedImage);
      if (!validation.isValid) {
        throw new Error(`Image validation failed: ${validation.errors.join(', ')}`);
      }

      console.log('✅ Image compression and validation complete');

      // Generate unique filename
      const fileName = this.generateFileName(userId, 'transaction', 'jpg');
      
      // Convert image to blob for upload
      const response = await fetch(compressedImage.uri);
      const blob = await response.blob();

      console.log(`📤 Uploading to Supabase: ${fileName}`);
      console.log(`📊 File size: ${ImageCompressionService.formatFileSize(blob.size)}`);

      // Upload to Supabase Storage
      console.log(`📤 Attempting upload to bucket: ${this.BUCKETS.TRANSACTION_IMAGES}`);
      console.log(`📄 File name: ${fileName}`);
      console.log(`📊 Blob size: ${blob.size} bytes`);
      console.log(`📋 Content type: image/jpeg`);

      const { data, error } = await supabase.storage
        .from(this.BUCKETS.TRANSACTION_IMAGES)
        .upload(fileName, blob, {
          contentType: 'image/jpeg',
          upsert: false, // Don't overwrite existing files
        });

      if (error) {
        console.error('❌ Supabase upload error details:', {
          message: error.message,
          statusCode: error.statusCode,
          error: error.error,
          details: error.details,
        });
        throw new Error(`Upload failed: ${error.message} (${error.statusCode || 'Unknown status'})`);
      }

      console.log('✅ Upload successful:', data.path);

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.BUCKETS.TRANSACTION_IMAGES)
        .getPublicUrl(data.path);

      // Record usage
      const updatedUsage = await UsageTrackingService.recordImageUpload(userId);

      console.log('🎉 Transaction image upload complete!');

      return {
        success: true,
        fileName: data.path,
        publicUrl: urlData.publicUrl,
        originalSize: compressedImage.originalSize,
        compressedSize: compressedImage.compressedSize,
        compressionRatio: compressedImage.compressionRatio,
        usage: updatedUsage,
        uploadedAt: new Date().toISOString(),
      };

    } catch (error) {
      console.error('❌ Error uploading transaction image:', error);
      throw error;
    }
  }

  /**
   * Upload image with progress tracking
   * @param {string} imageUri - Local image URI
   * @param {string} userId - User ID
   * @param {Function} onProgress - Progress callback
   * @returns {Promise<Object>} Upload result
   */
  static async uploadTransactionImageWithProgress(imageUri, userId, onProgress) {
    try {
      // Step 1: Usage check (10%)
      onProgress && onProgress(10, 'Checking usage limits...');
      const usageCheck = await UsageTrackingService.checkImageUploadLimit(userId);
      if (!usageCheck.canUpload) {
        throw new Error(`Daily image limit reached (${usageCheck.limit}). Resets in ${UsageTrackingService.getTimeUntilReset()}`);
      }

      // Step 2: Compression (50%)
      onProgress && onProgress(30, 'Compressing image...');
      const compressedImage = await ImageCompressionService.compressImage(imageUri);
      
      onProgress && onProgress(50, 'Validating image...');
      const validation = ImageCompressionService.validateCompressedImage(compressedImage);
      if (!validation.isValid) {
        throw new Error(`Image validation failed: ${validation.errors.join(', ')}`);
      }

      // Step 3: Upload preparation (70%)
      onProgress && onProgress(70, 'Preparing upload...');
      const fileName = this.generateFileName(userId, 'transaction', 'jpg');

      // For React Native, we'll upload the file directly using the URI
      // Supabase supports direct file upload from URI in React Native

      // Step 4: Upload (90%)
      onProgress && onProgress(80, 'Uploading to server...');

      // Read file as base64 and convert to proper format
      const base64 = await FileSystem.readAsStringAsync(compressedImage.uri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Convert base64 to Uint8Array for proper binary upload
      const binaryString = atob(base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      console.log('📤 Uploading binary data:', {
        originalSize: base64.length,
        binarySize: bytes.length,
        fileName: fileName
      });

      const { data, error } = await supabase.storage
        .from(this.BUCKETS.TRANSACTION_IMAGES)
        .upload(fileName, bytes, {
          contentType: 'image/jpeg',
          upsert: false,
        });

      if (error) {
        throw new Error(`Upload failed: ${error.message}`);
      }

      // Step 5: Finalization (100%)
      onProgress && onProgress(95, 'Finalizing...');
      const { data: urlData } = supabase.storage
        .from(this.BUCKETS.TRANSACTION_IMAGES)
        .getPublicUrl(data.path);

      const updatedUsage = await UsageTrackingService.recordImageUpload(userId);

      onProgress && onProgress(100, 'Upload complete!');

      return {
        success: true,
        fileName: data.path,
        publicUrl: urlData.publicUrl,
        originalSize: compressedImage.originalSize,
        compressedSize: compressedImage.compressedSize,
        compressionRatio: compressedImage.compressionRatio,
        usage: updatedUsage,
        uploadedAt: new Date().toISOString(),
      };

    } catch (error) {
      onProgress && onProgress(0, `Error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete image from storage
   * @param {string} fileName - File path in storage
   * @returns {Promise<boolean>} Success status
   */
  static async deleteTransactionImage(fileName) {
    try {
      if (!fileName) {
        console.log('⚠️ No filename provided for deletion');
        return true; // Consider it successful if no file to delete
      }

      const { error } = await supabase.storage
        .from(this.BUCKETS.TRANSACTION_IMAGES)
        .remove([fileName]);

      if (error) {
        console.error('❌ Error deleting image:', error);
        return false;
      }

      console.log('✅ Image deleted successfully:', fileName);
      return true;
    } catch (error) {
      console.error('❌ Error deleting image:', error);
      return false;
    }
  }

  /**
   * Delete multiple images from storage
   * @param {Array<string>} fileNames - Array of file paths
   * @returns {Promise<Object>} Result with success count
   */
  static async deleteMultipleImages(fileNames) {
    try {
      if (!fileNames || fileNames.length === 0) {
        return { success: true, deletedCount: 0, failedCount: 0 };
      }

      // Filter out empty/null filenames
      const validFileNames = fileNames.filter(name => name && name.trim());

      if (validFileNames.length === 0) {
        return { success: true, deletedCount: 0, failedCount: 0 };
      }

      const { data, error } = await supabase.storage
        .from(this.BUCKETS.TRANSACTION_IMAGES)
        .remove(validFileNames);

      if (error) {
        console.error('❌ Error deleting multiple images:', error);
        return { success: false, deletedCount: 0, failedCount: validFileNames.length, error: error.message };
      }

      const deletedCount = data?.length || 0;
      const failedCount = validFileNames.length - deletedCount;

      console.log(`✅ Bulk deletion complete: ${deletedCount} deleted, ${failedCount} failed`);

      return {
        success: true,
        deletedCount,
        failedCount,
        deletedFiles: data
      };

    } catch (error) {
      console.error('❌ Error in bulk image deletion:', error);
      return {
        success: false,
        deletedCount: 0,
        failedCount: fileNames?.length || 0,
        error: error.message
      };
    }
  }

  /**
   * Get storage usage statistics
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Storage usage stats
   */
  static async getStorageUsage(userId) {
    try {
      // List all files for user
      const { data, error } = await supabase.storage
        .from(this.BUCKETS.TRANSACTION_IMAGES)
        .list(userId, {
          limit: 1000,
          sortBy: { column: 'created_at', order: 'desc' },
        });

      if (error) {
        console.warn('Error getting storage usage:', error);
        return { totalFiles: 0, totalSize: 0 };
      }

      const totalFiles = data?.length || 0;
      const totalSize = data?.reduce((sum, file) => sum + (file.metadata?.size || 0), 0) || 0;

      return {
        totalFiles,
        totalSize,
        formattedSize: ImageCompressionService.formatFileSize(totalSize),
      };
    } catch (error) {
      console.error('Error getting storage usage:', error);
      return { totalFiles: 0, totalSize: 0 };
    }
  }

  /**
   * Create storage buckets if they don't exist (admin function)
   * @returns {Promise<void>}
   */
  static async initializeStorageBuckets() {
    try {
      const buckets = Object.values(this.BUCKETS);
      
      for (const bucketName of buckets) {
        const { data, error } = await supabase.storage.createBucket(bucketName, {
          public: true,
          allowedMimeTypes: bucketName === this.BUCKETS.TRANSACTION_IMAGES 
            ? ['image/jpeg', 'image/jpg'] 
            : ['audio/mp4', 'audio/mpeg'],
        });

        if (error && !error.message.includes('already exists')) {
          console.error(`Error creating bucket ${bucketName}:`, error);
        } else {
          console.log(`✅ Bucket ${bucketName} ready`);
        }
      }
    } catch (error) {
      console.error('Error initializing storage buckets:', error);
    }
  }

  /**
   * Test compression and upload workflow
   * @param {string} testImageUri - Test image URI
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Test results
   */
  static async testCompressionWorkflow(testImageUri, userId) {
    try {
      console.log('🧪 Testing compression workflow...');
      
      const startTime = Date.now();
      
      // Test compression
      const compressedImage = await ImageCompressionService.compressImage(testImageUri);
      const compressionTime = Date.now() - startTime;
      
      // Test validation
      const validation = ImageCompressionService.validateCompressedImage(compressedImage);
      
      // Test usage check
      const usageCheck = await UsageTrackingService.checkImageUploadLimit(userId);
      
      const results = {
        compression: {
          success: true,
          originalSize: compressedImage.originalSize,
          compressedSize: compressedImage.compressedSize,
          compressionRatio: compressedImage.compressionRatio,
          timeMs: compressionTime,
        },
        validation: validation,
        usage: usageCheck,
        totalTimeMs: Date.now() - startTime,
      };
      
      console.log('🧪 Test results:', results);
      return results;
      
    } catch (error) {
      console.error('❌ Test failed:', error);
      throw error;
    }
  }
}

export default StorageService;
