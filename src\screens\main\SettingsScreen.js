import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/styles';
import { useAuth } from '../../context/AuthContext';
import AppHeader from '../../components/AppHeader';

export default function SettingsScreen({ navigation }) {
  const { t, i18n } = useTranslation();
  const { user, logout } = useAuth();
  const [notifications, setNotifications] = useState(true);

  const languages = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'hi', name: 'Hindi', nativeName: 'हिंदी' },
    { code: 'mr', name: 'Marathi', nativeName: 'मराठी' },
    { code: 'bn', name: 'Bengali', nativeName: 'বাংলা' },
    { code: 'ta', name: 'Tamil', nativeName: 'தமிழ்' },
  ];

  const handleLanguageChange = (languageCode) => {
    i18n.changeLanguage(languageCode);
    Alert.alert('Language Changed', `Language changed to ${languages.find(l => l.code === languageCode)?.name}`);
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            const result = await logout();
            if (!result.success) {
              Alert.alert('Error', result.error || 'Failed to logout');
            }
          },
        },
      ]
    );
  };

  const showPremiumFeature = (featureName) => {
    Alert.alert(
      'Premium Feature',
      `${featureName} is available in the premium version. Upgrade to unlock this feature.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Learn More', onPress: () => console.log('Navigate to premium info') },
      ]
    );
  };

  const renderSettingItem = ({ icon, title, subtitle, onPress, rightComponent, isPremium = false }) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingLeft}>
        <Icon name={icon} size={24} color={colors.text} />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>
            {title} {isPremium && '🔒'}
          </Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightComponent || <Icon name="chevron-right" size={24} color={colors.placeholder} />}
    </TouchableOpacity>
  );

  const renderLanguageSelector = () => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Language / भाषा</Text>
      {languages.map((language) => (
        <TouchableOpacity
          key={language.code}
          style={styles.languageItem}
          onPress={() => handleLanguageChange(language.code)}
        >
          <Text style={styles.languageName}>{language.nativeName}</Text>
          <Text style={styles.languageEnglish}>({language.name})</Text>
          {i18n.language === language.code && (
            <Icon name="check" size={20} color={colors.primary} />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Global Header */}
      <AppHeader />

      <View style={globalStyles.screenContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
        </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Profile Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Profile</Text>
          <View style={styles.profileCard}>
            <View style={styles.profileIcon}>
              <Icon name="person" size={32} color={colors.primary} />
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>{user?.email || 'User'}</Text>
              <Text style={styles.profileEmail}>{user?.email}</Text>
            </View>
          </View>
        </View>

        {/* Language Section */}
        {renderLanguageSelector()}

        {/* Cash Box Management */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Cash Box Management</Text>
          
          {renderSettingItem({
            icon: 'account-balance-wallet',
            title: 'Manage Cash Boxes',
            subtitle: 'Add, edit, or delete cash boxes',
            onPress: () => showPremiumFeature('Multiple Cash Boxes'),
            isPremium: true,
          })}
          
          {renderSettingItem({
            icon: 'people',
            title: 'Manage Custodians',
            subtitle: 'Add, edit, or delete custodians',
            onPress: () => showPremiumFeature('Multiple Custodians'),
            isPremium: true,
          })}
          
          {renderSettingItem({
            icon: 'category',
            title: 'Expense Categories',
            subtitle: 'Customize expense categories',
            onPress: () => showPremiumFeature('Custom Categories'),
            isPremium: true,
          })}
        </View>

        {/* App Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>App Settings</Text>
          
          {renderSettingItem({
            icon: 'notifications',
            title: 'Notifications',
            subtitle: 'Manage notification preferences',
            rightComponent: (
              <Switch
                value={notifications}
                onValueChange={setNotifications}
                trackColor={{ false: colors.surface, true: colors.primary }}
                thumbColor={colors.background}
              />
            ),
          })}
          
          {renderSettingItem({
            icon: 'backup',
            title: 'Data Backup',
            subtitle: 'Cloud sync and backup',
            onPress: () => showPremiumFeature('Cloud Backup'),
            isPremium: true,
          })}
          
          {renderSettingItem({
            icon: 'delete-sweep',
            title: 'Clear Data',
            subtitle: 'Remove all transactions and data',
            onPress: () => Alert.alert('Clear Data', 'This feature will be available soon'),
          })}
        </View>

        {/* Support & Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support & Information</Text>
          
          {renderSettingItem({
            icon: 'help',
            title: 'Help & Support',
            subtitle: 'Get help and contact support',
            onPress: () => Alert.alert('Help', 'Help documentation coming soon'),
          })}
          
          {renderSettingItem({
            icon: 'info',
            title: 'About',
            subtitle: 'App version and information',
            onPress: () => Alert.alert('About', 'KharchaPani v1.0.0\nPetty Cash Management App'),
          })}
          
          {renderSettingItem({
            icon: 'star',
            title: 'Upgrade to Premium',
            subtitle: 'Unlock all features',
            onPress: () => Alert.alert('Premium', 'Premium features coming soon!'),
          })}
        </View>

        {/* Logout */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Icon name="logout" size={24} color={colors.background} />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>

        <View style={styles.bottomPadding} />
      </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 12,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: colors.placeholder,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingText: {
    marginLeft: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  settingSubtitle: {
    fontSize: 14,
    color: colors.placeholder,
    marginTop: 2,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  languageName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    flex: 1,
  },
  languageEnglish: {
    fontSize: 14,
    color: colors.placeholder,
    marginRight: 8,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.background,
    marginLeft: 8,
  },
  bottomPadding: {
    height: 40,
  },
});
