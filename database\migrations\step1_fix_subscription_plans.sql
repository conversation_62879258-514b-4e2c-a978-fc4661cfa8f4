-- =====================================================
-- STEP 1: Fix subscription_plans table
-- =====================================================
-- Add missing columns and update plans

-- 1. Add missing columns to subscription_plans
ALTER TABLE subscription_plans 
ADD COLUMN IF NOT EXISTS cash_box_limit integer NOT NULL DEFAULT 1;

ALTER TABLE subscription_plans 
ADD COLUMN IF NOT EXISTS daily_image_limit integer NOT NULL DEFAULT 3;

ALTER TABLE subscription_plans 
ADD COLUMN IF NOT EXISTS updated_at timestamp with time zone DEFAULT now();

-- 2. Update existing plans with new column values
INSERT INTO subscription_plans (plan_name, plan_code, custodian_limit, cash_box_limit, daily_image_limit, features, price_monthly, is_active)
VALUES 
  ('free', 'FREE', 1, 1, 3, '{"voice_memos": false, "advanced_reports": false, "description_field": false, "image_upload": true, "transaction_management": true}', 0, true),
  ('plan_1', 'PLAN1', 5, 3, 10, '{"voice_memos": true, "advanced_reports": true, "description_field": true, "image_upload": true, "transaction_management": true}', 299, true),
  ('plan_2', 'PLAN2', 10, 5, 20, '{"voice_memos": true, "advanced_reports": true, "description_field": true, "image_upload": true, "transaction_management": true}', 499, true),
  ('enterprise', 'ENTERPRISE', 999, 999, 100, '{"voice_memos": true, "advanced_reports": true, "description_field": true, "image_upload": true, "transaction_management": true}', 999, true)
ON CONFLICT (plan_name) DO UPDATE SET
  cash_box_limit = EXCLUDED.cash_box_limit,
  daily_image_limit = EXCLUDED.daily_image_limit,
  features = EXCLUDED.features,
  price_monthly = EXCLUDED.price_monthly,
  is_active = EXCLUDED.is_active;

-- 3. Update existing users to have free plan (if they don't have one)
UPDATE user_roles 
SET subscription_plan_id = (SELECT id FROM subscription_plans WHERE plan_name = 'free')
WHERE subscription_plan_id IS NULL;
