import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Image,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/globalStyles';
import { useAuth } from '../../context/AuthContext';
import AppHeader from '../../components/AppHeader';

export default function RegisterScreen({ navigation }) {
  const { t } = useTranslation();
  const { register, loading } = useAuth();
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validateForm = () => {
    const newErrors = {};

    // Full name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required';
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters';
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    const result = await register(
      formData.email.trim(),
      formData.password,
      { full_name: formData.fullName.trim() }
    );

    if (result.success) {
      if (result.needsConfirmation) {
        Alert.alert(
          'Registration Successful',
          'Please check your email to confirm your account before signing in.',
          [{ text: 'OK', onPress: () => navigation.navigate('Login') }]
        );
      } else {
        Alert.alert(
          'Registration Successful',
          'Your account has been created successfully!',
          [{ text: 'OK' }]
        );
      }
    } else {
      Alert.alert('Registration Failed', result.error || 'Please try again.');
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <View style={styles.keyboardContainer}>
      {/* Global Header */}
      <AppHeader />

      <KeyboardAvoidingView
        style={globalStyles.screenContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Subtitle */}
          <View style={styles.subtitleContainer}>
            <Text style={styles.subtitle}>
              Record and Manage your Petty Cash Transactions on Project Sites efficiently
            </Text>
          </View>

          {/* Form Container */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>{t('createAccount')}</Text>

            {/* Full Name Input - Compact */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>{t('fullName')}</Text>
              <View style={[styles.compactInputWrapper, errors.fullName && styles.inputError]}>
                <Icon name="person" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder={t('enterFullName')}
                  placeholderTextColor={colors.placeholder}
                  value={formData.fullName}
                  onChangeText={(value) => updateFormData('fullName', value)}
                  autoCapitalize="words"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>
              {errors.fullName && <Text style={styles.errorText}>{errors.fullName}</Text>}
            </View>

            {/* Email Input - Compact */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>{t('email')}</Text>
              <View style={[styles.compactInputWrapper, errors.email && styles.inputError]}>
                <Icon name="email" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder={t('enterEmail')}
                  placeholderTextColor={colors.placeholder}
                  value={formData.email}
                  onChangeText={(value) => updateFormData('email', value)}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>
              {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
            </View>

            {/* Password Input - Compact */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>{t('password')}</Text>
              <View style={[styles.compactInputWrapper, errors.password && styles.inputError]}>
                <Icon name="lock" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder={t('enterPassword')}
                  placeholderTextColor={colors.placeholder}
                  value={formData.password}
                  onChangeText={(value) => updateFormData('password', value)}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeIcon}
                >
                  <Icon
                    name={showPassword ? 'visibility' : 'visibility-off'}
                    size={18}
                    color={colors.placeholder}
                  />
                </TouchableOpacity>
              </View>
              {errors.password && <Text style={styles.errorText}>{errors.password}</Text>}
            </View>

            {/* Confirm Password Input - Compact */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>Confirm Password</Text>
              <View style={[styles.compactInputWrapper, errors.confirmPassword && styles.inputError]}>
                <Icon name="lock" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder="Confirm your password"
                  placeholderTextColor={colors.placeholder}
                  value={formData.confirmPassword}
                  onChangeText={(value) => updateFormData('confirmPassword', value)}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
                <TouchableOpacity
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                  style={styles.eyeIcon}
                >
                  <Icon
                    name={showConfirmPassword ? 'visibility' : 'visibility-off'}
                    size={18}
                    color={colors.placeholder}
                  />
                </TouchableOpacity>
              </View>
              {errors.confirmPassword && <Text style={styles.errorText}>{errors.confirmPassword}</Text>}
            </View>

            {/* Create Account Button - Compact */}
            <TouchableOpacity
              style={[styles.compactSignInButton, loading && styles.disabledButton]}
              onPress={handleRegister}
              disabled={loading}
            >
              <Text style={styles.signInButtonText}>
                {loading ? 'Creating Account...' : t('createAccountButton')}
              </Text>
            </TouchableOpacity>

            {/* Login Link - Compact */}
            <View style={styles.compactRegisterContainer}>
              <Text style={styles.registerPrompt}>{t('alreadyHaveAccount')}</Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('Login')}
                disabled={loading}
                style={styles.registerLinkButton}
              >
                <Text style={styles.registerLink}>{t('signInButton')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  subtitleContainer: {
    marginBottom: 20,
    paddingHorizontal: 8,
  },
  subtitle: {
    fontSize: 14, // Smaller compact font
    color: colors.onSurface,
    textAlign: 'center',
    lineHeight: 20, // Compact line height
  },
  formContainer: {
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    padding: 16, // More compact padding
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 2,
    elevation: 3,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.onSurface, // Global heading color
    textAlign: 'center',
    marginBottom: 16,
  },
  // Compact Input Styles
  compactInputContainer: {
    marginBottom: 12,
  },
  compactLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text, // Global text color
    marginBottom: 4,
  },
  compactInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 4, // Global 4px border radius
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: 10, // More compact padding
    height: 40, // More compact height
  },
  inputError: {
    borderColor: colors.error,
  },
  inputIcon: {
    marginRight: 8,
  },
  compactTextInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text, // Global text color
    paddingVertical: 0,
  },
  eyeIcon: {
    padding: 4,
  },
  errorText: {
    fontSize: 12,
    color: colors.error,
    marginTop: 2,
    marginLeft: 4,
  },
  // Compact Button and Link Styles
  compactSignInButton: {
    backgroundColor: colors.primary,
    borderRadius: 4, // Global 4px border radius
    paddingVertical: 10, // More compact padding
    alignItems: 'center',
    marginBottom: 12, // More compact spacing
    marginTop: 6, // More compact spacing
  },
  signInButtonText: {
    color: colors.background,
    fontSize: 14, // Smaller compact font
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  compactRegisterContainer: {
    flexDirection: 'column', // Changed to column for next line
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  registerLinkButton: {
    marginTop: 4, // Space between prompt and link
  },
  registerPrompt: {
    fontSize: 12, // Smaller compact font
    color: colors.text, // Global text color
    marginRight: 4,
  },
  registerLink: {
    fontSize: 14,
    color: colors.primary, // Keep primary color for links
    fontWeight: '600',
  },
});
