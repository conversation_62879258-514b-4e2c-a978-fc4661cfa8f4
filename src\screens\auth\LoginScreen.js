import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  ScrollView,
  Platform,
  Image,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { colors } from '../../constants/colors';
import { globalStyles } from '../../constants/globalStyles';
import { useAuth } from '../../context/AuthContext';
import AppHeader from '../../components/AppHeader';

export default function LoginScreen({ navigation }) {
  const { t } = useTranslation();
  const { login, loading } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);

  const validateForm = () => {
    const newErrors = {};

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    const result = await login(formData.email.trim(), formData.password);

    if (!result.success) {
      Alert.alert('Login Failed', result.error || 'Please check your credentials and try again.');
    }
  };

  const updateFormData = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <View style={globalStyles.screenContainer}>
      {/* Header with Logo, Notification, and Avatar */}
      <AppHeader
        showNotification={true}
        showAvatar={true}
        onNotificationPress={() => Alert.alert('Notifications', 'No new notifications')}
        onAvatarPress={() => Alert.alert('Profile', 'Profile options')}
      />

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Subtitle */}
          <View style={styles.subtitleContainer}>
            <Text style={styles.subtitle}>{t('appSubtitle')}</Text>
          </View>

          {/* Compact Form Container */}
          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>{t('signIn')}</Text>

            {/* Email Input - Compact */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>{t('email')}</Text>
              <View style={[styles.compactInputWrapper, errors.email && styles.inputError]}>
                <Icon name="email" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder={t('enterEmail')}
                  placeholderTextColor={colors.placeholder}
                  value={formData.email}
                  onChangeText={(value) => updateFormData('email', value)}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
              </View>
              {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
            </View>

            {/* Password Input - Compact */}
            <View style={styles.compactInputContainer}>
              <Text style={styles.compactLabel}>{t('password')}</Text>
              <View style={[styles.compactInputWrapper, errors.password && styles.inputError]}>
                <Icon name="lock" size={18} color={colors.placeholder} style={styles.inputIcon} />
                <TextInput
                  style={styles.compactTextInput}
                  placeholder={t('enterPassword')}
                  placeholderTextColor={colors.placeholder}
                  value={formData.password}
                  onChangeText={(value) => updateFormData('password', value)}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  editable={!loading}
                />
                <TouchableOpacity
                  onPress={() => setShowPassword(!showPassword)}
                  style={styles.eyeIcon}
                >
                  <Icon
                    name={showPassword ? 'visibility' : 'visibility-off'}
                    size={18}
                    color={colors.placeholder}
                  />
                </TouchableOpacity>
              </View>
              {errors.password && <Text style={styles.errorText}>{errors.password}</Text>}
            </View>

            {/* Forgot Password - Compact */}
            <TouchableOpacity
              style={styles.compactForgotPassword}
              onPress={() => Alert.alert('Reset Password', 'Password reset functionality coming soon!')}
            >
              <Text style={styles.forgotPasswordText}>{t('resetPassword')}</Text>
            </TouchableOpacity>

            {/* Login Button - Compact */}
            <TouchableOpacity
              style={[styles.compactSignInButton, loading && styles.disabledButton]}
              onPress={handleLogin}
              disabled={loading}
            >
              <Text style={styles.signInButtonText}>
                {loading ? 'Signing In...' : t('signInButton')}
              </Text>
            </TouchableOpacity>

            {/* Register Link - Compact */}
            <View style={styles.compactRegisterContainer}>
              <Text style={styles.registerPrompt}>{t('newToApp')}</Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('Register')}
                disabled={loading}
              >
                <Text style={styles.registerLink}>{t('createAccountButton')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  subtitleContainer: {
    marginBottom: 20,
    paddingHorizontal: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 20,
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 4,
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 16,
  },
  // Compact Input Styles
  compactInputContainer: {
    marginBottom: 12,
  },
  compactLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  compactInputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: 12,
    height: 44,
  },
  inputError: {
    borderColor: colors.error,
  },
  inputIcon: {
    marginRight: 8,
  },
  compactTextInput: {
    flex: 1,
    fontSize: 16,
    color: colors.text,
    paddingVertical: 0,
  },
  eyeIcon: {
    padding: 4,
  },
  errorText: {
    fontSize: 12,
    color: colors.error,
    marginTop: 2,
    marginLeft: 4,
  },
  // Compact Button and Link Styles
  compactForgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: 16,
    marginTop: 4,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  compactSignInButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  signInButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.6,
  },
  compactRegisterContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
    flexWrap: 'wrap',
  },
  registerPrompt: {
    fontSize: 14,
    color: colors.textSecondary,
    marginRight: 4,
  },
  registerLink: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '600',
  },
});
