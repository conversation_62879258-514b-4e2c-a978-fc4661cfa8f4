import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useAuth } from '../context/AuthContext';
import { colors } from '../constants/colors';

/**
 * RoleGuard Component - Conditionally renders content based on user role
 * 
 * Usage Examples:
 * 
 * // Show content only for premium users
 * <RoleGuard roles={['premium_user', 'admin', 'superadmin']}>
 *   <PremiumFeature />
 * </RoleGuard>
 * 
 * // Show content only for admins
 * <RoleGuard roles={['admin', 'superadmin']}>
 *   <AdminPanel />
 * </RoleGuard>
 * 
 * // Show fallback for unauthorized users
 * <RoleGuard 
 *   roles={['premium_user']} 
 *   fallback={<UpgradePrompt />}
 * >
 *   <PremiumFeature />
 * </RoleGuard>
 */

const RoleGuard = ({ 
  roles = [], 
  children, 
  fallback = null,
  showFallback = true,
  fallbackMessage = "You don't have permission to access this feature."
}) => {
  const { userRole, hasAnyRole } = useAuth();

  // If no roles specified, show content (open access)
  if (roles.length === 0) {
    return children;
  }

  // Check if user has required role
  const hasAccess = hasAnyRole(roles);

  if (hasAccess) {
    return children;
  }

  // User doesn't have access
  if (fallback) {
    return fallback;
  }

  if (showFallback) {
    return (
      <View style={styles.fallbackContainer}>
        <Text style={styles.fallbackText}>{fallbackMessage}</Text>
      </View>
    );
  }

  // Don't render anything
  return null;
};

/**
 * FeatureGuard Component - Conditionally renders content based on feature access
 * 
 * Usage Examples:
 * 
 * // Show content only if user can access voice memos
 * <FeatureGuard feature="voice_memos">
 *   <VoiceMemoButton />
 * </FeatureGuard>
 * 
 * // Show upgrade prompt for premium features
 * <FeatureGuard 
 *   feature="advanced_reports" 
 *   fallback={<UpgradePrompt />}
 * >
 *   <AdvancedReports />
 * </FeatureGuard>
 */
export const FeatureGuard = ({ 
  feature, 
  children, 
  fallback = null,
  showFallback = true,
  fallbackMessage = "This feature requires a premium subscription."
}) => {
  const { hasFeatureAccess } = useAuth();

  const hasAccess = hasFeatureAccess(feature);

  if (hasAccess) {
    return children;
  }

  // User doesn't have access
  if (fallback) {
    return fallback;
  }

  if (showFallback) {
    return (
      <View style={styles.fallbackContainer}>
        <Text style={styles.fallbackText}>{fallbackMessage}</Text>
      </View>
    );
  }

  // Don't render anything
  return null;
};

/**
 * AdminOnly Component - Shorthand for admin/superadmin only content
 */
export const AdminOnly = ({ children, fallback = null }) => {
  return (
    <RoleGuard 
      roles={['admin', 'superadmin']} 
      fallback={fallback}
      fallbackMessage="Admin access required."
    >
      {children}
    </RoleGuard>
  );
};

/**
 * SuperAdminOnly Component - Shorthand for superadmin only content
 */
export const SuperAdminOnly = ({ children, fallback = null }) => {
  return (
    <RoleGuard 
      roles={['superadmin']} 
      fallback={fallback}
      fallbackMessage="Superadmin access required."
    >
      {children}
    </RoleGuard>
  );
};

/**
 * PremiumOnly Component - Shorthand for premium users only
 */
export const PremiumOnly = ({ children, fallback = null }) => {
  return (
    <RoleGuard 
      roles={['premium_user', 'admin', 'superadmin']} 
      fallback={fallback}
      fallbackMessage="Premium subscription required."
    >
      {children}
    </RoleGuard>
  );
};

const styles = StyleSheet.create({
  fallbackContainer: {
    padding: 16,
    backgroundColor: colors.surface,
    borderRadius: 4, // Global 4px border radius
    borderWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 8,
  },
  fallbackText: {
    fontSize: 14,
    color: colors.text, // Global text color
    textAlign: 'center',
    fontStyle: 'italic',
  },
});

export default RoleGuard;
